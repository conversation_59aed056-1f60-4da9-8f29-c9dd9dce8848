# Pokemon TCG Card Enrichment System

Ce système permet d'enrichir automatiquement la base de données des cartes Pokemon TCG avec des données provenant de diverses sources, notamment Bulbapedia. Il orchestre plusieurs étapes de scraping, de fusion de données et de mise à jour de la base de données.

## Structure du Projet

```
.
├── README.md                 # Documentation
├── main.py                   # Script principal orchestrant le workflow
├── .env                      # Fichier de configuration des identifiants (BD, etc.)
├── requirements.txt          # Dépendances Python du projet
├── set_mapping.json         # Mapping centralisé des sets (si encore utilisé, sinon à vérifier)
├── bulbapedia_all_cards.json # Données Bulbapedia (potentiellement généré par un script)
├── cache/                    # Dossier de cache pour les requêtes réseau
├── database/                 # Scripts SQL pour la structure de la BD
└── pipeline/                 # Modules Python pour les différentes étapes (scrape, enrich, inject)
```

## Installation et Environnement

Il est fortement recommandé d'utiliser un environnement virtuel Python pour gérer les dépendances du projet.

1.  **Cloner le repository :**
    ```bash
    git clone <url_du_repository>
    cd pokemon-tcg-scraper
    ```

2.  **Créer un environnement virtuel :**
    ```bash
    python3 -m venv .venv
    ```

3.  **Activer l'environnement virtuel :**
    *   Sur macOS et Linux :
        ```bash
        source .venv/bin/activate
        ```
    *   Sur Windows :
        ```bash
        .venv\Scripts\activate
        ```

4.  **Installer les dépendances :**
    Une fois l'environnement activé, installez les packages requis :
    ```bash
    pip3 install -r requirements.txt
    ```

## Configuration de la Base de Données

Le système nécessite un serveur MySQL local pour stocker et gérer les données des cartes.

1.  **Serveur MySQL Local :**
    Assurez-vous d'avoir un serveur MySQL en cours d'exécution. Des outils comme MAMP, WAMP, LAMP, ou une installation directe de MySQL peuvent être utilisés.

2.  **Fichier d'environnement `.env` :**
    Créez un fichier `.env` à la racine du projet pour stocker vos identifiants de base de données. Copiez l'exemple ci-dessous et adaptez les valeurs à votre configuration :

    ```dotenv
    DB_HOST="localhost"
    DB_PORT="3306"       # Ou le port de votre serveur MySQL
    DB_NAME="pokemon_tcg_db"
    DB_USER="root"
    DB_PASSWORD="your_password" # Remplacez par votre mot de passe
    DB_UNIX_SOCKET=""    # Laissez vide si non utilisé, sinon spécifiez le chemin du socket (ex: /tmp/mysql.sock)
    ```
    Le script principal (`main.py`) chargera automatiquement ces variables.

3.  **(Optionnel) Configuration du Serveur MCP `local-mysql` pour Kilo Code :**
    Pour permettre à des outils comme Kilo Code d'interagir directement avec votre base de données, vous pouvez installer et configurer le serveur MCP `local-mysql`.
    *   **Installation :**
        ```bash
        pip3 install mysql-mcp-server uv
        ```
    *   **Configuration dans `mcp_settings.json` :**
        Localisez ou créez le fichier `mcp_settings.json` (généralement dans `/Users/<USER>/Library/Application Support/Code/User/globalStorage/kilocode.kilo-code/settings/mcp_settings.json` sur macOS). Ajoutez ou modifiez la configuration pour `local-mysql` :
        ```json
        {
          "servers": [
            {
              "name": "local-mysql",
              "command": ["uvx", "--from", "mysql-mcp-server", "mysql_mcp_server"],
              "env": {
                "MYSQL_HOST": "localhost", // ou votre DB_HOST
                "MYSQL_PORT": "3306",     // ou votre DB_PORT
                "MYSQL_USER": "root",     // ou votre DB_USER
                "MYSQL_PASSWORD": "your_password", // ou votre DB_PASSWORD
                "MYSQL_DATABASE": "pokemon_tcg_db" // ou votre DB_NAME
                // "MYSQL_UNIX_SOCKET": "/tmp/mysql.sock" // Décommentez et adaptez si vous utilisez un socket Unix
              },
              "enabled": true
            }
            // ... autres serveurs MCP
          ]
        }
        ```
        Assurez-vous que les variables d'environnement (`MYSQL_HOST`, etc.) correspondent à celles de votre fichier `.env` ou à votre configuration MySQL.

## Fichiers de Configuration Principaux

### set_mapping.json (si applicable)
Ce fichier peut être utilisé pour centraliser le mapping entre les différents codes de sets. (Vérifier son utilisation actuelle dans le projet).

```json
{
    "metadata": {
        "version": "1.0",
        "last_updated": "YYYY-MM-DD"
    },
    "sets": {
        "S7R": {
            "bulbapedia": { /* ... */ },
            "thai": { /* ... */ },
            "japanese": { /* ... */ }
        }
        // ...
    }
}
```

### bulbapedia_all_cards.json (si applicable)
Ce fichier peut contenir les données extraites de Bulbapedia. (Vérifier s'il est généré ou utilisé comme source statique).

```json
{
    "S7R": {
        "cards": [
            {
                "card_number": "001",
                "name_en": "Rayquaza V",
                "rarity": "RR",
                "url": "https://bulbapedia.bulbagarden.net/wiki/..."
            }
            // ...
        ]
    }
    // ...
}
```

## Utilisation et Workflow

Le script principal `main.py` orchestre les différentes étapes du processus d'enrichissement.

### Exécution

Le script `main.py` accepte plusieurs arguments pour contrôler son comportement. Voici quelques exemples courants :

*   **Exécuter toutes les étapes du pipeline (scraping, fusion, enrichissement, mise à jour BD) :**
    ```bash
    python3 main.py --all
    ```

*   **Exécuter uniquement l'étape de scraping de Bulbapedia :**
    ```bash
    python3 main.py --only-scrape
    ```

*   **Exécuter uniquement l'étape d'enrichissement des cartes (après scraping et fusion) :**
    ```bash
    python3 main.py --enrich-cards
    ```

*   **Mettre à jour la base de données avec les données enrichies :**
    ```bash
    python3 main.py --update-db
    ```

*   **Afficher l'aide pour voir toutes les options disponibles :**
    ```bash
    python3 main.py --help
    ```

Consultez l'aide du script (`python3 main.py --help`) pour une liste complète des commandes et de leurs descriptions.

## Structure de la Base de Données

Le système interagit principalement avec les tables suivantes :

*   `pokemon_cards` : Contient les informations détaillées de chaque carte.
    *   `name_en` : Nom anglais de la carte.
    *   `rarity` : Rareté de la carte.
    *   `card_url` : URL Bulbapedia de la carte (ou autre source).
    *   ... (autres champs pertinents)

*   `pokemon_expansions` : Contient les informations sur les extensions (sets).
    *   `id` : Identifiant unique de l'extension.
    *   `code` : Code de l'extension (par exemple, "S7R"). `VARCHAR(255)`.
    *   `name` : Nom de l'extension (généralement le nom anglais, `name_en`). `VARCHAR(255)`.
    *   `source` : Source de l'information pour cette extension (par exemple, "bulbapedia", "thai_api"). `VARCHAR(255)`.
    *   ... (autres champs comme `release_date`, `total_cards`, etc.)

La structure exacte et les champs peuvent être consultés dans les fichiers SQL du dossier `database/`.

## Gestion des Erreurs

Le script `main.py` et ses modules intègrent une gestion des erreurs pour :
- Les connexions à la base de données.
- Les données manquantes ou incorrectes dans les fichiers sources.
- Les problèmes lors du scraping (erreurs réseau, changements de structure des sites).
- Les erreurs lors des insertions ou mises à jour en base de données.

Les erreurs sont généralement logguées dans la console.

## Maintenance

### Ajouter un Nouveau Set / Mettre à Jour les Données
Le workflow géré par `main.py` devrait prendre en charge l'ajout de nouveaux sets ou la mise à jour des données existantes si les sources sont correctement configurées et accessibles. Reportez-vous aux options de `main.py` pour relancer les étapes nécessaires.

## Gestion des Données Spécifiques par Langue

Cette section détaille les processus spécifiques à la collecte et à la gestion des données de cartes Pokémon TCG pour certaines langues.

### Données Thaïlandaises

La récupération et l'utilisation des données provenant du site officiel Pokémon TCG thaïlandais impliquent plusieurs étapes spécifiques :

1.  **Scraping des Données :**
    *   Le script [`scraping/scraping_thai.py`](scraping/scraping_thai.py:1) est responsable de la collecte des données depuis le site officiel thaïlandais.
    *   **Gestion de la Progression :**
        *   Pour éviter de retraiter l'intégralité des pages à chaque exécution, le script sauvegarde la dernière page traitée avec succès dans un fichier nommé `scraping_thai_progress.txt` (situé à la racine du projet).
        *   Lorsqu'il est lancé, le script vérifie automatiquement la présence de ce fichier et, s'il existe, reprend le scraping à partir de la page indiquée. Cela permet une reprise efficace en cas d'interruption.
        *   Pour forcer le script à ignorer la progression sauvegardée et à recommencer le scraping depuis la toute première page, utilisez l'argument `--reset-progress` lors de son exécution :
            ```bash
            python scraping/scraping_thai.py --reset-progress
            ```
            Cette option est utile si vous souhaitez vous assurer que toutes les données sont re-scrapées, par exemple après une modification majeure du script ou du site source. L'ancien fichier de progression sera alors écrasé ou ignoré.
    *   Par défaut, ce script est configuré pour stocker les données récupérées dans une base de données MySQL. La configuration initiale suppose un environnement MAMP, mais peut être adaptée à d'autres configurations MySQL.

2.  **Génération du Fichier `thai_cards.json` :**
    *   Le fichier `thai_cards.json`, qui est utilisé par le script [`cross_multilang_cards.py`](cross_multilang_cards.py:1) (via l'argument `--official thai_cards.json`), **n'est pas généré automatiquement** par le script [`scraping/scraping_thai.py`](scraping/scraping_thai.py:1).
    *   Ce fichier doit être créé manuellement ou via un processus d'exportation distinct à partir de la base de données MySQL où les données thaïlandaises ont été stockées.

3.  **Format du Fichier `thai_cards.json` :**
    *   Le fichier `thai_cards.json` doit être un dictionnaire JSON.
    *   Les clés de ce dictionnaire sont les codes de set (par exemple, `"S7R"`, `"SV5K_T"`).
    *   Les valeurs associées à chaque clé sont des listes de cartes appartenant à ce set. Chaque carte dans la liste est généralement représentée par un objet JSON contenant ses détails. Exemple de structure attendue :
        ```json
        {
          "SV5K_T": [
            {
              "card_number": "001",
              "name_th": "ชื่อการ์ดภาษาไทย",
              // ... autres champs de la carte
            },
            // ... autres cartes du set SV5K_T
          ],
          "S7R": [
            {
              "card_number": "001",
              "name_th": "ชื่อการ์ดภาษาไทยอื่น",
              // ... autres champs de la carte
            },
            // ... autres cartes du set S7R
          ]
          // ... autres sets
        }
        ```

4.  **Utilisation dans le Croisement Multilingue :**
    *   Une fois `thai_cards.json` correctement formaté et disponible, il peut être utilisé comme source de données officielles thaïlandaises par le script [`cross_multilang_cards.py`](cross_multilang_cards.py:1) en utilisant l'argument `--official thai_cards.json`.