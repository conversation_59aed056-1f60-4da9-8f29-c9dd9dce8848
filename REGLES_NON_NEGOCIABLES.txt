===============================================================================
                    RÈGLES NON NÉGOCIABLES - POKEMON TCG SCRAPER
===============================================================================

🚨 CES RÈGLES SONT ABSOLUES ET NON NÉGOCIABLES 🚨

===============================================================================
1. ARCHITECTURE DES SCRAPERS
===============================================================================

✅ STRUCTURE OBLIGATOIRE :
   • UN SEUL scraper Bulbapedia : scraping/scraping_bulbapedia.py
   • UN scraper PAR LANGUE : scraping/scraping_[langue].py
     - scraping/scraping_thai.py
     - scraping/scraping_indonesian.py
     - scraping/scraping_japanese.py (futur)
     - scraping/scraping_korean.py (futur)
     - scraping/scraping_chinese.py (futur)

❌ INTERDIT :
   • Multiples fichiers pour une même source
   • Scripts spécialisés par set
   • Scripts temporaires ou de test
   • Fichiers de correction ponctuels

===============================================================================
2. SOURCES DE DONNÉES
===============================================================================

✅ SOURCES AUTORISÉES UNIQUEMENT :
   • Bulbapedia.org (données de référence)
   • Sites officiels Pokemon par région :
     - https://asia.pokemon-card.com/th/ (Thaï)
     - https://asia.pokemon-card.com/id/ (Indonésien)
     - https://asia.pokemon-card.com/ja/ (Japonais - futur)
     - https://asia.pokemon-card.com/ko/ (Coréen - futur)
     - https://asia.pokemon-card.com/zh/ (Chinois - futur)

❌ SOURCES INTERDITES :
   • Sites non officiels
   • APIs tierces
   • Bases de données externes
   • Fichiers manuels

===============================================================================
3. PRINCIPE DE SCRAPING COMPLET
===============================================================================

✅ OBLIGATION DE SCRAPING TOTAL :
   • TOUS les sets disponibles sur chaque site
   • TOUTES les cartes de chaque set
   • AUCUNE limitation artificielle
   • AUCUN filtrage par date ou critère

❌ INTERDIT :
   • Scraping partiel ou limité
   • Sélection manuelle de sets
   • Arrêt prématuré du scraping
   • Limitation par nombre de cartes

===============================================================================
4. GESTION DE LA PAGINATION
===============================================================================

✅ MÉTHODES OBLIGATOIRES :
   • Pagination complète jusqu'à épuisement
   • Gestion des ranges d'IDs quand applicable
   • Récupération de 100% des cartes disponibles
   • Vérification de complétude

❌ INTERDIT :
   • Limitation à 20 cartes par set
   • Arrêt à la première page
   • Pagination incomplète
   • Accepter des résultats partiels

===============================================================================
5. INDÉPENDANCE DES SCRAPERS
===============================================================================

✅ CHAQUE SCRAPER DOIT :
   • Fonctionner de manière autonome
   • Créer ses propres sets si nécessaire
   • Gérer sa propre logique de déduplication
   • Être exécutable indépendamment

❌ INTERDIT :
   • Dépendances entre scrapers
   • Prérequis d'exécution d'autres scrapers
   • Logique partagée critique

===============================================================================
6. ÉVOLUTIVITÉ MULTILINGUE
===============================================================================

✅ ARCHITECTURE ÉVOLUTIVE :
   • Support facile d'ajout de nouvelles langues
   • Colonnes de base de données extensibles
   • Logique web_id universelle
   • Système de fallback intelligent

❌ INTERDIT :
   • Code spécifique à une langue
   • Logique non extensible
   • Hardcoding de langues spécifiques

===============================================================================
7. GESTION DES ERREURS
===============================================================================

✅ ROBUSTESSE OBLIGATOIRE :
   • Gestion complète des erreurs réseau
   • Retry automatique en cas d'échec
   • Logging détaillé de tous les problèmes
   • Continuation malgré les erreurs ponctuelles

❌ INTERDIT :
   • Arrêt du scraper sur erreur ponctuelle
   • Absence de gestion d'erreur
   • Logging insuffisant

===============================================================================
8. PERFORMANCE ET RESPECT DES SITES
===============================================================================

✅ BONNES PRATIQUES :
   • Pauses entre requêtes (0.5-2 secondes)
   • User-Agent approprié
   • Respect des limites de taux
   • Gestion intelligente des timeouts

❌ INTERDIT :
   • Requêtes trop rapides (spam)
   • Absence de pauses
   • Surcharge des serveurs

===============================================================================
9. COHÉRENCE DE LA BASE DE DONNÉES
===============================================================================

✅ INTÉGRITÉ OBLIGATOIRE :
   • web_id unique par source
   • Déduplication intelligente
   • Cohérence des données multilingues
   • Système de fallback entre langues

❌ INTERDIT :
   • Doublons de cartes
   • Incohérences entre langues
   • Données corrompues

===============================================================================
10. MAINTENANCE ET ÉVOLUTION
===============================================================================

✅ PRINCIPES DE MAINTENANCE :
   • Code lisible et documenté
   • Fonctions modulaires et réutilisables
   • Configuration centralisée
   • Tests et validation automatiques

❌ INTERDIT :
   • Code spaghetti
   • Logique dispersée
   • Configuration hardcodée
   • Absence de documentation

===============================================================================
                                RÉSUMÉ EXÉCUTIF
===============================================================================

🎯 OBJECTIF : Un système de scraping Pokemon TCG multilingue, complet, robuste
              et évolutif qui récupère 100% des données disponibles sur les
              sites officiels.

📁 STRUCTURE : 
   • 1 scraper Bulbapedia
   • 1 scraper par langue officielle
   • 0 fichier temporaire ou spécialisé

🌐 SOURCES : Uniquement sites officiels Pokemon et Bulbapedia

📊 RÉSULTAT : Base de données complète avec toutes les cartes disponibles
              dans toutes les langues supportées

===============================================================================
                        ⚠️  VIOLATION = REFACTORING COMPLET ⚠️
===============================================================================

Toute violation de ces règles entraîne un refactoring complet du projet
pour revenir à l'architecture définie ci-dessus.

Ces règles garantissent :
• Simplicité d'utilisation
• Complétude des données  
• Évolutivité du système
• Maintenance facilitée
• Performance optimale

===============================================================================
                                   FIN
===============================================================================
