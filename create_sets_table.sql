-- Création de la table pokemon_sets pour gérer correctement les sets
-- Cette table remplace la logique foireuse des ranges d'IDs

CREATE TABLE IF NOT EXISTS pokemon_sets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    
    -- Identifiants du set
    code VARCHAR(20) NOT NULL UNIQUE,           -- Code officiel (ex: SC1D, SV1s, etc.)
    name_en VARCHAR(255),                       -- Nom anglais officiel
    name_th VARCHAR(255),                       -- Nom thaï
    name_id VARCHAR(255),                       -- Nom indonésien
    name_ja VARCHAR(255),                       -- Nom japonais
    name_ko VARCHAR(255),                       -- Nom coréen
    name_zh VARCHAR(255),                       -- Nom chinois
    
    -- Métadonnées du set
    release_date DATE,                          -- Date de sortie
    total_cards INT,                            -- Nombre total de cartes
    series VARCHAR(100),                        -- <PERSON><PERSON> (ex: Sword & Shield, Scarlet & Violet)
    set_type ENUM('main', 'starter', 'promo', 'special') DEFAULT 'main',
    
    -- URLs et ressources
    logo_url VARCHAR(500),                      -- URL du logo du set
    symbol_url VARCHAR(500),                    -- URL du symbole du set
    
    -- Ranges d'IDs par région (pour mapping automatique)
    id_range_th_start INT,                      -- Début range thaï
    id_range_th_end INT,                        -- Fin range thaï
    id_range_id_start INT,                      -- Début range indonésien
    id_range_id_end INT,                        -- Fin range indonésien
    id_range_ja_start INT,                      -- Début range japonais
    id_range_ja_end INT,                        -- Fin range japonais
    
    -- Statut et source
    status ENUM('active', 'discontinued', 'upcoming') DEFAULT 'active',
    source VARCHAR(50) NOT NULL,                -- Source de découverte
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Index pour performance
    INDEX idx_code (code),
    INDEX idx_series (series),
    INDEX idx_release_date (release_date),
    INDEX idx_th_range (id_range_th_start, id_range_th_end),
    INDEX idx_id_range (id_range_id_start, id_range_id_end)
);

-- Mise à jour de la table pokemon_cards pour référencer pokemon_sets
ALTER TABLE pokemon_cards 
ADD COLUMN set_id INT,
ADD FOREIGN KEY (set_id) REFERENCES pokemon_sets(id);

-- Migration des données existantes (optionnel)
-- Les anciennes données dans pokemon_expansions peuvent être migrées vers pokemon_sets

-- Exemples d'insertion de vrais sets
INSERT INTO pokemon_sets (
    code, name_en, name_th, series, set_type, total_cards, 
    id_range_th_start, id_range_th_end, source
) VALUES 
('SC1D', 'V Starter Deck Sword & Shield', 'V Starter Deck "ซอร์ด แอนด์ ชีลด์"', 'Sword & Shield', 'starter', 164, 400, 599, 'pokemon_tcg'),
('SV1s', 'Scarlet & Violet Base Set', 'สการ์เล็ต แอนด์ ไวโอเล็ต', 'Scarlet & Violet', 'main', 198, 5000, 5199, 'pokemon_tcg'),
('SV2a', 'Pokemon Card 151', 'โปเกมอนการ์ด 151', 'Scarlet & Violet', 'special', 165, 6000, 6199, 'pokemon_tcg');

-- Vue pour compatibilité avec l'ancien système
CREATE VIEW pokemon_expansions_view AS
SELECT 
    id,
    code,
    name_en as name,
    source,
    created_at,
    updated_at
FROM pokemon_sets;
