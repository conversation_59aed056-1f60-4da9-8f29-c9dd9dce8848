#!/usr/bin/env python3
"""
Test direct de sauvegarde de cartes Thai avec la nouvelle structure
"""

import mysql.connector
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.database import get_db_config

def get_language_id(connection, language_code):
    """Récupère l'ID de la langue"""
    cursor = connection.cursor(dictionary=True)
    cursor.execute("SELECT id FROM languages WHERE code = %s", (language_code,))
    result = cursor.fetchone()
    return result['id'] if result else None

def get_or_create_expansion(connection, set_code, set_name_en=None):
    """Récupère ou crée une expansion dans la nouvelle table expansions"""
    cursor = connection.cursor(dictionary=True)

    # Vérifier si l'expansion existe déjà
    cursor.execute("SELECT id FROM expansions WHERE code = %s", (set_code,))
    existing_expansion = cursor.fetchone()

    if existing_expansion:
        return existing_expansion['id']

    # Créer la nouvelle expansion
    cursor.execute("""
        INSERT INTO expansions (code, name_en, region)
        VALUES (%s, %s, 'Thailand')
    """, (set_code, set_name_en))

    connection.commit()
    return cursor.lastrowid

def get_or_create_card(connection, expansion_id, card_number, supertype='Pokémon'):
    """Récupère ou crée une carte maître"""
    cursor = connection.cursor(dictionary=True)
    
    # Chercher une carte existante avec ce numéro dans cette expansion
    cursor.execute("""
        SELECT id FROM cards 
        WHERE canonical_number = %s AND expansion_id = %s
    """, (card_number, expansion_id))
    
    existing_card = cursor.fetchone()
    if existing_card:
        return existing_card['id']
    
    # Créer une nouvelle carte maître
    cursor.execute("""
        INSERT INTO cards (canonical_number, expansion_id, supertype)
        VALUES (%s, %s, %s)
    """, (card_number, expansion_id, supertype))
    
    connection.commit()
    return cursor.lastrowid

def save_thai_cards_direct():
    """Sauvegarde directe de quelques cartes Thai"""
    
    print("🇹🇭 SAUVEGARDE DIRECTE CARTES THAI")
    print("=" * 50)
    
    # Connexion base de données
    try:
        connection = mysql.connector.connect(**get_db_config())
        print("✅ Connexion à la base de données établie")
    except Exception as e:
        print(f"❌ Erreur connexion base: {e}")
        return
    
    # Récupérer l'ID de la langue thaï
    thai_language_id = get_language_id(connection, 'th')
    if not thai_language_id:
        print("❌ Erreur: Langue thaï non trouvée dans la base")
        return
    
    print(f"✅ Langue thaï trouvée avec ID: {thai_language_id}")
    
    # Cartes Thai réelles du scraper
    thai_cards = [
        {
            'web_id': '1',
            'name_th': 'สไตรค์',
            'card_number': '001/154',
            'image_url': 'https://asia.pokemon-card.com/th/card-img/1.jpg',
            'set_code': 'SV6A',
            'set_name_en': 'Night Wanderer'
        },
        {
            'web_id': '2',
            'name_th': 'อโกจิมูชิ',
            'card_number': '002/154',
            'image_url': 'https://asia.pokemon-card.com/th/card-img/2.jpg',
            'set_code': 'SV6A',
            'set_name_en': 'Night Wanderer'
        },
        {
            'web_id': '155',
            'name_th': 'คาเตอร์ปี',
            'card_number': '001/153',
            'image_url': 'https://asia.pokemon-card.com/th/card-img/155.jpg',
            'set_code': 'SV5A',
            'set_name_en': 'Crimson Haze'
        }
    ]
    
    saved_count = 0
    
    for card in thai_cards:
        print(f"\n📝 Sauvegarde: {card['name_th']} ({card['card_number']})")
        
        # Récupérer ou créer l'expansion
        expansion_id = get_or_create_expansion(
            connection,
            card['set_code'],
            card['set_name_en']
        )
        print(f"   📦 Expansion ID: {expansion_id}")

        # Récupérer ou créer la carte maître
        card_id = get_or_create_card(
            connection,
            expansion_id,
            card['card_number']
        )
        print(f"   🃏 Carte maître ID: {card_id}")

        # Créer la version thaï de la carte
        cursor = connection.cursor()
        cursor.execute("""
            INSERT INTO card_versions (
                card_id, language_id, name, image_url, web_id
            ) VALUES (%s, %s, %s, %s, %s)
        """, (
            card_id,
            thai_language_id,
            card['name_th'],
            card['image_url'],
            card['web_id']
        ))

        saved_count += 1
        print(f"   ✅ Version thaï créée avec ID: {cursor.lastrowid}")

    connection.commit()
    connection.close()
    
    print(f"\n✅ SAUVEGARDE TERMINÉE: {saved_count} cartes thaï sauvegardées")
    
    # Vérifier les données en base
    print(f"\n🔍 VÉRIFICATION EN BASE:")
    connection = mysql.connector.connect(**get_db_config())
    cursor = connection.cursor()
    
    cursor.execute("SELECT COUNT(*) FROM expansions")
    expansions_count = cursor.fetchone()[0]
    print(f"   • Expansions: {expansions_count}")
    
    cursor.execute("SELECT COUNT(*) FROM cards")
    cards_count = cursor.fetchone()[0]
    print(f"   • Cartes maîtres: {cards_count}")
    
    cursor.execute("SELECT COUNT(*) FROM card_versions WHERE language_id = %s", (thai_language_id,))
    thai_versions_count = cursor.fetchone()[0]
    print(f"   • Versions thaï: {thai_versions_count}")
    
    # Afficher quelques exemples
    cursor.execute("""
        SELECT cv.name, cv.web_id, c.canonical_number, e.code 
        FROM card_versions cv 
        JOIN cards c ON cv.card_id = c.id 
        JOIN expansions e ON c.expansion_id = e.id 
        WHERE cv.language_id = %s 
        LIMIT 5
    """, (thai_language_id,))
    
    examples = cursor.fetchall()
    print(f"\n📋 Exemples de cartes thaï:")
    for example in examples:
        print(f"   • {example[0]} ({example[2]}) - Set {example[3]} - Web ID {example[1]}")
    
    connection.close()

if __name__ == "__main__":
    save_thai_cards_direct()
