"""
Configuration centralisée pour le projet Pokémon TCG
"""

# Configuration de la base de données
DATABASE_CONFIG = {
    'host': 'localhost',
    'user': 'pokemon_user',
    'password': 'password',
    'database': 'pokemon_tcg'
}

# Configuration du scraping
SCRAPING_CONFIG = {
    'delay': 1.5,
    'cache_enabled': True,
    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
}

# Sources de données
SOURCES = {
    'bulbapedia': 'https://bulbapedia.bulbagarden.net/wiki/Category:TCG_sets',
    'thai': 'https://asia.pokemon-card.com/th/card-search/list/'
}

# Mappings des sets
SET_MAPPINGS_FILE = 'config/set_mappings.json'

# Chemins des fichiers de sortie
OUTPUT_DIR = 'output'