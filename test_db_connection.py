#!/usr/bin/env python3
"""
Script de test de connexion à la base de données Pokemon TCG
"""

import mysql.connector
from mysql.connector import Error
from config.database import get_db_config, get_connection_string
import sys

def test_database_connection():
    """Test la connexion à la base de données et affiche les informations"""
    
    print("🔌 TEST DE CONNEXION À LA BASE DE DONNÉES")
    print("=" * 50)
    
    # Afficher la configuration
    config = get_db_config()
    connection_string = get_connection_string()
    
    print(f"📍 Host: {config['host']}")
    print(f"🔢 Port: {config['port']}")
    print(f"🗄️  Database: {config['database']}")
    print(f"👤 User: {config['user']}")
    print(f"🔗 Connection String: {connection_string}")
    
    if config.get('unix_socket'):
        print(f"🔌 Unix Socket: {config['unix_socket']}")
    
    print("\n" + "=" * 50)
    
    try:
        # Tentative de connexion
        print("🔄 Tentative de connexion...")
        connection = mysql.connector.connect(**config)
        
        if connection.is_connected():
            print("✅ CONNEXION RÉUSSIE!")
            
            # Informations sur la base de données
            cursor = connection.cursor()
            
            # Version MySQL
            cursor.execute("SELECT VERSION()")
            mysql_version = cursor.fetchone()[0]
            print(f"🐬 Version MySQL: {mysql_version}")
            
            # Nom de la base de données actuelle
            cursor.execute("SELECT DATABASE()")
            current_db = cursor.fetchone()[0]
            print(f"🗄️  Base de données actuelle: {current_db}")
            
            # Lister les tables
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            
            if tables:
                print(f"\n📋 Tables disponibles ({len(tables)}):")
                for table in tables:
                    table_name = table[0]
                    
                    # Compter les enregistrements dans chaque table
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                        count = cursor.fetchone()[0]
                        print(f"   • {table_name}: {count:,} enregistrements")
                    except Error as e:
                        print(f"   • {table_name}: Erreur lors du comptage ({e})")
            else:
                print("\n⚠️  Aucune table trouvée dans la base de données")
            
            # Test d'une requête simple sur les tables principales
            print("\n🔍 Test des tables principales:")

            main_tables = ['pokemon_cards', 'pokemon_expansions']
            for table in main_tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    print(f"   ✅ {table}: {count:,} enregistrements")

                    # Afficher quelques exemples
                    if count > 0:
                        cursor.execute(f"SELECT * FROM {table} LIMIT 3")
                        # Consommer tous les résultats pour éviter "Unread result found"
                        results = cursor.fetchall()
                        columns = [desc[0] for desc in cursor.description]
                        print(f"      Colonnes: {', '.join(columns[:5])}{'...' if len(columns) > 5 else ''}")

                except Error as e:
                    print(f"   ❌ {table}: Table non trouvée ou erreur ({e})")

            cursor.close()
            
        else:
            print("❌ Connexion échouée (statut non connecté)")
            
    except Error as e:
        print(f"❌ ERREUR DE CONNEXION: {e}")
        print("\n🔧 Solutions possibles:")
        print("   1. Vérifiez que MySQL/MAMP est démarré")
        print("   2. Vérifiez les paramètres dans le fichier .env")
        print("   3. Vérifiez que la base de données 'pokemon_tcg_db' existe")
        print("   4. Vérifiez les permissions de l'utilisateur")
        
        return False
        
    except Exception as e:
        print(f"❌ ERREUR INATTENDUE: {e}")
        return False
        
    finally:
        if 'connection' in locals() and connection.is_connected():
            connection.close()
            print("\n🔌 Connexion fermée")
    
    return True

def create_database_if_not_exists():
    """Crée la base de données si elle n'existe pas"""
    
    print("\n🏗️  CRÉATION DE LA BASE DE DONNÉES SI NÉCESSAIRE")
    print("=" * 50)
    
    config = get_db_config()
    db_name = config['database']
    
    # Configuration sans spécifier la base de données
    config_no_db = config.copy()
    del config_no_db['database']
    
    try:
        connection = mysql.connector.connect(**config_no_db)
        cursor = connection.cursor()
        
        # Vérifier si la base existe
        cursor.execute("SHOW DATABASES")
        databases = [db[0] for db in cursor.fetchall()]
        
        if db_name in databases:
            print(f"✅ La base de données '{db_name}' existe déjà")
        else:
            print(f"🏗️  Création de la base de données '{db_name}'...")
            cursor.execute(f"CREATE DATABASE {db_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print(f"✅ Base de données '{db_name}' créée avec succès!")
        
        cursor.close()
        connection.close()
        
    except Error as e:
        print(f"❌ Erreur lors de la création de la base: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🃏 POKEMON TCG SCRAPER - TEST DE CONNEXION DB")
    print("=" * 60)
    
    # Créer la base si nécessaire
    if "--create-db" in sys.argv:
        create_database_if_not_exists()
        print()
    
    # Test de connexion
    success = test_database_connection()
    
    if success:
        print("\n🎉 Test de connexion terminé avec succès!")
        sys.exit(0)
    else:
        print("\n💥 Test de connexion échoué!")
        sys.exit(1)
