# Composer
/vendor/
composer.lock

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Environment
.env
.venv
env/
venv/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Logs
*.log
logs/

# Cache
data/proxy_cache.json
images/

# Database
*.sqlite
*.db

# OS
Thumbs.db

# Temporary files
*.tmp
*.temp
*.bak

# Données
*.json
*.csv
*.sqlite3

# Tests
.coverage
htmlcov/
.pytest_cache/
.mypy_cache/

# Fichiers temporaires
*.swp
*~ 