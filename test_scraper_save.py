#!/usr/bin/env python3
"""
Test simple de sauvegarde avec la nouvelle structure multilingue
"""

import mysql.connector
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.database import get_db_config

def get_language_id(connection, language_code):
    """Récupère l'ID de la langue"""
    cursor = connection.cursor(dictionary=True)
    cursor.execute("SELECT id FROM languages WHERE code = %s", (language_code,))
    result = cursor.fetchone()
    return result['id'] if result else None

def get_or_create_expansion(connection, set_code, set_name_en=None):
    """Récupère ou crée une expansion dans la nouvelle table expansions"""
    cursor = connection.cursor(dictionary=True)

    # Vérifier si l'expansion existe déjà
    cursor.execute("SELECT id FROM expansions WHERE code = %s", (set_code,))
    existing_expansion = cursor.fetchone()

    if existing_expansion:
        print(f"   ✅ Set {set_code} existe déjà avec ID: {existing_expansion['id']}")
        return existing_expansion['id']

    print(f"🆕 Création de la nouvelle expansion: {set_code} - {set_name_en}")

    cursor.execute("""
        INSERT INTO expansions (code, name_en, region)
        VALUES (%s, %s, 'Thailand')
    """, (set_code, set_name_en))

    connection.commit()
    return cursor.lastrowid

def get_or_create_card(connection, expansion_id, card_number, supertype='Pokémon'):
    """Récupère ou crée une carte maître"""
    cursor = connection.cursor(dictionary=True)
    
    # Chercher une carte existante avec ce numéro dans cette expansion
    cursor.execute("""
        SELECT id FROM cards 
        WHERE canonical_number = %s AND expansion_id = %s
    """, (card_number, expansion_id))
    
    existing_card = cursor.fetchone()
    if existing_card:
        return existing_card['id']
    
    # Créer une nouvelle carte maître
    cursor.execute("""
        INSERT INTO cards (canonical_number, expansion_id, supertype)
        VALUES (%s, %s, %s)
    """, (card_number, expansion_id, supertype))
    
    connection.commit()
    return cursor.lastrowid

def card_exists_in_db(connection, web_id, language_id):
    """Vérifie si une carte existe déjà en base pour cette langue"""
    cursor = connection.cursor()
    cursor.execute("""
        SELECT cv.id FROM card_versions cv 
        WHERE cv.web_id = %s AND cv.language_id = %s
    """, (web_id, language_id))
    return cursor.fetchone() is not None

def test_save_cards():
    """Test de sauvegarde de quelques cartes"""
    
    print("🧪 TEST DE SAUVEGARDE AVEC NOUVELLE STRUCTURE")
    print("=" * 50)
    
    # Connexion base de données
    try:
        connection = mysql.connector.connect(**get_db_config())
        print("✅ Connexion à la base de données établie")
    except Exception as e:
        print(f"❌ Erreur connexion base: {e}")
        return
    
    # Récupérer l'ID de la langue thaï
    thai_language_id = get_language_id(connection, 'th')
    if not thai_language_id:
        print("❌ Erreur: Langue thaï non trouvée dans la base")
        return
    
    print(f"✅ Langue thaï trouvée avec ID: {thai_language_id}")
    
    # Cartes de test
    test_cards = [
        {
            'web_id': 'test_1',
            'name_th': 'สไตรค์ทดสอบ',
            'card_number': '001/154',
            'image_url': 'https://example.com/image1.jpg',
            'set_code': 'TEST_SET',
            'set_name_en': 'Test Set'
        },
        {
            'web_id': 'test_2',
            'name_th': 'อโกจิมูชิทดสอบ',
            'card_number': '002/154',
            'image_url': 'https://example.com/image2.jpg',
            'set_code': 'TEST_SET',
            'set_name_en': 'Test Set'
        }
    ]
    
    saved_count = 0
    skipped_count = 0
    
    for card in test_cards:
        print(f"\n📝 Traitement carte: {card['name_th']} ({card['card_number']})")
        
        # Vérifier si la carte existe déjà pour cette langue
        if card_exists_in_db(connection, card['web_id'], thai_language_id):
            print(f"   ⚠️  Carte {card['web_id']} existe déjà")
            skipped_count += 1
            continue

        # Récupérer ou créer l'expansion
        expansion_id = get_or_create_expansion(
            connection,
            card['set_code'],
            card['set_name_en']
        )

        # Récupérer ou créer la carte maître
        card_id = get_or_create_card(
            connection,
            expansion_id,
            card['card_number']
        )

        # Créer la version thaï de la carte
        cursor = connection.cursor()
        cursor.execute("""
            INSERT INTO card_versions (
                card_id, language_id, name, image_url, web_id
            ) VALUES (%s, %s, %s, %s, %s)
        """, (
            card_id,
            thai_language_id,
            card['name_th'],
            card['image_url'],
            card['web_id']
        ))

        saved_count += 1
        print(f"   ✅ Version thaï créée avec ID: {cursor.lastrowid}")

    connection.commit()
    connection.close()
    
    print(f"\n✅ TEST TERMINÉ:")
    print(f"   • {saved_count} nouvelles cartes sauvegardées")
    print(f"   • {skipped_count} cartes déjà existantes (ignorées)")
    
    # Vérifier les données en base
    print(f"\n🔍 VÉRIFICATION EN BASE:")
    connection = mysql.connector.connect(**get_db_config())
    cursor = connection.cursor()
    
    cursor.execute("SELECT COUNT(*) FROM expansions")
    expansions_count = cursor.fetchone()[0]
    print(f"   • Expansions: {expansions_count}")
    
    cursor.execute("SELECT COUNT(*) FROM cards")
    cards_count = cursor.fetchone()[0]
    print(f"   • Cartes maîtres: {cards_count}")
    
    cursor.execute("SELECT COUNT(*) FROM card_versions")
    versions_count = cursor.fetchone()[0]
    print(f"   • Versions de cartes: {versions_count}")
    
    cursor.execute("SELECT COUNT(*) FROM card_versions WHERE language_id = %s", (thai_language_id,))
    thai_versions_count = cursor.fetchone()[0]
    print(f"   • Versions thaï: {thai_versions_count}")
    
    connection.close()

if __name__ == "__main__":
    test_save_cards()
