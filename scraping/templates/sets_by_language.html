<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sets Pokemon TCG - Vue par Langue</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>🌐 Sets Pokemon TCG - Vue par Langue</h1>
        
        <div class="row mb-3">
            <div class="col">
                <a href="/sets" class="btn btn-outline-primary">Vue Standard</a>
                <a href="/sets/unified" class="btn btn-primary">Vue par Langue</a>
                <a href="/sets/by-language/english" class="btn btn-outline-success">Ang<PERSON>s</a>
                <a href="/sets/by-language/thai" class="btn btn-outline-warning">T<PERSON><PERSON></a>
                <a href="/sets/by-language/indonesian" class="btn btn-outline-info">Indonésien</a>
            </div>
        </div>
        
        <div class="row">
            {% for set in sets %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5>{{ set.code }}</h5>
                        <span class="badge bg-secondary">{{ set.primary_language }}</span>
                    </div>
                    <div class="card-body">
                        <h6 class="card-title">{{ set.name }}</h6>
                        <p class="card-text">
                            <strong>{{ set.card_count }}</strong> cartes<br>
                            <small class="text-muted">Source: {{ set.source }}</small>
                        </p>
                        
                        {% if set.related_sets %}
                        <div class="mt-2">
                            <small><strong>Sets liés:</strong></small><br>
                            {% for related in set.related_sets %}
                            <span class="badge bg-light text-dark me-1">{{ related.code }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                        
                        <a href="/sets/{{ set.code }}" class="btn btn-primary btn-sm mt-2">Voir les cartes</a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</body>
</html>