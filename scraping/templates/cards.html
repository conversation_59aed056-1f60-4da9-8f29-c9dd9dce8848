{% extends "base.html" %}

{% block title %}{{ set_info.name_en or set_info.name }} Cards - Pokemon TCG Database{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">🃏 {{ set_info.name_en or set_info.name }}</h1>
    {% if set_info.name_en and set_info.name != set_info.name_en %}
    <p style="color: #6b7280; font-size: 0.9rem; margin-top: 0.5rem;">{{ set_info.name }}</p>
    {% endif %}
    <p class="page-subtitle">
        <a href="{{ url_for('sets') }}" style="color: #4f46e5; text-decoration: none;">← Back to Sets</a> |
        {{ cards|length }} cards in this set
    </p>
</div>

<div class="content-card">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem; flex-wrap: wrap; gap: 1rem;">
        <h2 style="color: #1f2937; margin: 0;">Card Collection</h2>

        <form method="get" style="display: flex; gap: 1rem; align-items: center;">
            <div class="form-group" style="margin: 0;">
                <label for="sort" style="margin-bottom: 0.25rem;">Sort by:</label>
                <select name="sort" id="sort" style="width: auto; min-width: 120px;">
                    <option value="card_number" {% if sort == 'card_number' %}selected{% endif %}>Card Number</option>
                    <option value="name_th" {% if sort == 'name_th' %}selected{% endif %}>Thai Name</option>
                    <option value="name_en" {% if sort == 'name_en' %}selected{% endif %}>English Name</option>
                </select>
            </div>
            <div class="form-group" style="margin: 0;">
                <label for="order" style="margin-bottom: 0.25rem;">Order:</label>
                <select name="order" id="order" style="width: auto; min-width: 100px;">
                    <option value="asc" {% if order == 'asc' %}selected{% endif %}>Ascending</option>
                    <option value="desc" {% if order == 'desc' %}selected{% endif %}>Descending</option>
                </select>
            </div>
            <button type="submit" class="btn" style="margin-top: 1.5rem;">Sort</button>
        </form>
    </div>

    <div class="table-container">
        <table>
            <thead>
                <tr>
                    <th>Card Number</th>
                    <th>Thai Name</th>
                    <th>Indonesian Name</th>
                    <th>English Name</th>
                    <th style="min-width: 180px;">Images (🇬🇧🇹🇭🇮🇩)</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for c in cards %}
                <tr>
                    <td>
                        <strong style="color: #4f46e5;">{{ c.card_number or '-' }}</strong>
                    </td>
                    <td>{{ c.name_th or '-' }}</td>
                    <td>{{ c.name_id or '-' }}</td>
                    <td>{{ c.name_en or '-' }}</td>
                    <td style="padding: 8px;">
                        <div style="display: flex; gap: 4px; flex-wrap: wrap; align-items: center;">
                            {% if c.image_url %}
                                {% if '_T/' in c.image_url %}
                                    <!-- Image thaï stockée dans image_url -->
                                    <div style="text-align: center;">
                                        <img src="/{{ c.image_url }}" alt="{{ c.name_th or c.name_en or 'Card' }}" title="🇹🇭 Image thaï"
                                             onclick="openImageModal('/{{ c.image_url }}', '{{ c.name_th or c.name_en or 'Card' }}', '🇹🇭 Thai')"
                                             style="width: 50px; height: auto; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border: 2px solid #10b981; cursor: pointer;">
                                        <div style="font-size: 10px; color: #10b981; font-weight: bold; margin-top: 2px;">🇹🇭 TH</div>
                                    </div>
                                {% elif '_ID/' in c.image_url %}
                                    <!-- Image indonésienne stockée dans image_url -->
                                    <div style="text-align: center;">
                                        <img src="/{{ c.image_url }}" alt="{{ c.name_id or c.name_en or 'Card' }}" title="🇮🇩 Image indonésienne"
                                             onclick="openImageModal('/{{ c.image_url }}', '{{ c.name_id or c.name_en or 'Card' }}', '🇮🇩 Indonesian')"
                                             style="width: 50px; height: auto; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border: 2px solid #f59e0b; cursor: pointer;">
                                        <div style="font-size: 10px; color: #f59e0b; font-weight: bold; margin-top: 2px;">🇮🇩 ID</div>
                                    </div>
                                {% else %}
                                    <!-- Image anglaise -->
                                    <div style="text-align: center;">
                                        <img src="/{{ c.image_url }}" alt="{{ c.name_en or 'Card' }}" title="🇬🇧 Image anglaise"
                                             onclick="openImageModal('/{{ c.image_url }}', '{{ c.name_en or c.name_th or c.name_id or 'Card' }}', '🇬🇧 English')"
                                             style="width: 50px; height: auto; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border: 2px solid #3b82f6; cursor: pointer;">
                                        <div style="font-size: 10px; color: #3b82f6; font-weight: bold; margin-top: 2px;">🇬🇧 EN</div>
                                    </div>
                                {% endif %}
                            {% endif %}

                            {% if c.image_url_th and '_T/' not in (c.image_url or '') %}
                                <!-- Image thaï séparée (seulement si pas déjà affichée via image_url) -->
                                <div style="text-align: center;">
                                    <img src="/{{ c.image_url_th }}" alt="{{ c.name_th or c.name_en or 'Card' }}" title="🇹🇭 Image thaï"
                                         onclick="openImageModal('/{{ c.image_url_th }}', '{{ c.name_th or c.name_en or 'Card' }}', '🇹🇭 Thai')"
                                         style="width: 50px; height: auto; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border: 2px solid #10b981; cursor: pointer;">
                                    <div style="font-size: 10px; color: #10b981; font-weight: bold; margin-top: 2px;">🇹🇭 TH</div>
                                </div>
                            {% endif %}

                            {% if c.image_url_id and '_ID/' not in (c.image_url or '') %}
                                <!-- Image indonésienne séparée (seulement si pas déjà affichée via image_url) -->
                                <div style="text-align: center;">
                                    <img src="/{{ c.image_url_id }}" alt="{{ c.name_id or c.name_en or 'Card' }}" title="🇮🇩 Image indonésienne"
                                         onclick="openImageModal('/{{ c.image_url_id }}', '{{ c.name_id or c.name_en or 'Card' }}', '🇮🇩 Indonesian')"
                                         style="width: 50px; height: auto; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border: 2px solid #f59e0b; cursor: pointer;">
                                    <div style="font-size: 10px; color: #f59e0b; font-weight: bold; margin-top: 2px;">🇮🇩 ID</div>
                                </div>
                            {% endif %}

                            {% if not c.image_url_th and not c.image_url_id and not c.image_url %}
                                <span style="color: #9ca3af; font-size: 0.875rem;">No images</span>
                            {% endif %}
                        </div>
                    </td>
                    <td>
                        <span style="color: #9ca3af; font-size: 0.875rem;">Card #{{ c.card_number }}</span>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    {% if cards|length == 0 %}
    <div style="text-align: center; padding: 3rem; color: #6b7280;">
        <div style="font-size: 4rem; margin-bottom: 1rem;">🃏</div>
        <h3>No cards found</h3>
        <p>This set appears to be empty or cards haven't been scraped yet.</p>
        <div style="margin-top: 1.5rem;">
            <a href="{{ url_for('scraping_dashboard') }}" class="btn">Run Scraping</a>
        </div>
    </div>
    {% endif %}
</div>

<div class="content-card">
    <h3 style="color: #1f2937; margin-bottom: 1rem;">📊 Set Statistics</h3>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
        <div style="text-align: center; padding: 1rem; background: #f8fafc; border-radius: 8px;">
            <div style="font-size: 1.5rem; font-weight: bold; color: #4f46e5;">{{ cards|length }}</div>
            <div style="font-size: 0.875rem; color: #6b7280;">Total Cards</div>
        </div>
        <div style="text-align: center; padding: 1rem; background: #f8fafc; border-radius: 8px;">
            <div style="font-size: 1.5rem; font-weight: bold; color: #4f46e5;">
                {{ cards|selectattr('name_en')|list|length }}
            </div>
            <div style="font-size: 0.875rem; color: #6b7280;">With English Names</div>
        </div>
        <div style="text-align: center; padding: 1rem; background: #f8fafc; border-radius: 8px;">
            <div style="font-size: 1.5rem; font-weight: bold; color: #4f46e5;">
                {{ cards|selectattr('image_url')|list|length }}
            </div>
            <div style="font-size: 0.875rem; color: #6b7280;">With Images</div>
        </div>
        <div style="text-align: center; padding: 1rem; background: #f8fafc; border-radius: 8px;">
            <div style="font-size: 1.5rem; font-weight: bold; color: #4f46e5;">
                {{ cards|selectattr('card_number')|list|length }}
            </div>
            <div style="font-size: 0.875rem; color: #6b7280;">With Card Numbers</div>
        </div>
    </div>
</div>

<!-- Modal pour agrandir les images -->
<div id="imageModal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.8); cursor: pointer;" onclick="closeImageModal()">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); max-width: 90%; max-height: 90%; text-align: center;">
        <img id="modalImage" style="max-width: 100%; max-height: 80vh; border-radius: 8px; box-shadow: 0 4px 20px rgba(0,0,0,0.5);">
        <div id="modalCaption" style="color: white; font-size: 18px; font-weight: bold; margin-top: 15px; text-shadow: 2px 2px 4px rgba(0,0,0,0.8);"></div>
        <div style="color: #ccc; font-size: 14px; margin-top: 5px;">Cliquez n'importe où pour fermer</div>
    </div>
</div>

<script>
    function openImageModal(imageSrc, cardName, language) {
        const modal = document.getElementById('imageModal');
        const modalImage = document.getElementById('modalImage');
        const modalCaption = document.getElementById('modalCaption');

        modalImage.src = imageSrc;
        modalCaption.textContent = `${cardName} (${language})`;
        modal.style.display = 'block';

        // Empêcher le scroll de la page
        document.body.style.overflow = 'hidden';
    }

    function closeImageModal() {
        const modal = document.getElementById('imageModal');
        modal.style.display = 'none';

        // Restaurer le scroll de la page
        document.body.style.overflow = 'auto';
    }

    // Fermer avec la touche Escape
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closeImageModal();
        }
    });
</script>

{% endblock %}