<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Éditeur de Mappings en Masse - Pokemon TCG</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .set-row { border-bottom: 1px solid #eee; }
        .set-row:hover { background-color: #f8f9fa; }
        .has-cards { background-color: #d4edda; }
        .no-cards { background-color: #f8d7da; }
        .url-input { font-size: 0.9rem; }
        .set-code { font-weight: bold; color: #0d6efd; }
        .card-count { font-size: 0.8rem; color: #6c757d; }
        .sticky-header { position: sticky; top: 0; z-index: 100; }
        .bulk-actions { position: sticky; bottom: 0; background: white; border-top: 2px solid #dee2e6; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-cards-blank me-2"></i>Pokemon TCG Database
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/sets">
                    <i class="fas fa-layer-group me-1"></i>Sets
                </a>
                <a class="nav-link" href="/mapping">
                    <i class="fas fa-edit me-1"></i>Mapping Simple
                </a>
                <a class="nav-link active" href="/bulk-mapping">
                    <i class="fas fa-list-ul me-1"></i>Mapping en Masse
                </a>
                <a class="nav-link" href="/scraping">
                    <i class="fas fa-spider me-1"></i>Scraping
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white sticky-header">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h4 class="mb-0">
                                    <i class="fas fa-list-ul me-2"></i>Éditeur de Mappings en Masse
                                </h4>
                                <small>{{ total_sets }} sets au total</small>
                            </div>
                            <div class="col-md-6 text-end">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-light btn-sm" onclick="fillBulbapediaUrls()">
                                        <i class="fas fa-magic me-1"></i>Auto-remplir Bulbapedia
                                    </button>
                                    <button type="button" class="btn btn-outline-light btn-sm" onclick="clearAllUrls()">
                                        <i class="fas fa-trash me-1"></i>Vider tout
                                    </button>
                                    <button type="button" class="btn btn-outline-light btn-sm" onclick="filterSets()">
                                        <i class="fas fa-filter me-1"></i>Filtrer
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <!-- Filtres -->
                        <div class="p-3 bg-light border-bottom" id="filterPanel" style="display: none;">
                            <div class="row">
                                <div class="col-md-4">
                                    <input type="text" class="form-control" id="searchFilter" placeholder="Rechercher par code ou nom...">
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select" id="statusFilter">
                                        <option value="">Tous les sets</option>
                                        <option value="with-cards">Avec cartes</option>
                                        <option value="without-cards">Sans cartes</option>
                                        <option value="with-url">Avec URL</option>
                                        <option value="without-url">Sans URL</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select" id="eraFilter">
                                        <option value="">Toutes les ères</option>
                                        <option value="SV">Scarlet & Violet</option>
                                        <option value="SW">Sword & Shield</option>
                                        <option value="SM">Sun & Moon</option>
                                        <option value="XY">XY</option>
                                        <option value="BW">Black & White</option>
                                        <option value="DP">Diamond & Pearl</option>
                                        <option value="EX">EX</option>
                                        <option value="Classic">Classique</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-outline-secondary w-100" onclick="resetFilters()">
                                        <i class="fas fa-undo me-1"></i>Reset
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Liste des sets -->
                        <div class="table-responsive" style="max-height: 60vh; overflow-y: auto;">
                            <table class="table table-sm mb-0">
                                <thead class="table-dark sticky-top">
                                    <tr>
                                        <th style="width: 120px;">Code</th>
                                        <th style="width: 300px;">Nom du Set</th>
                                        <th style="width: 80px;">Cartes</th>
                                        <th>URL Bulbapedia</th>
                                        <th style="width: 100px;">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="setsTable">
                                    {% for set in sets %}
                                    <tr class="set-row {{ 'has-cards' if set.has_cards else 'no-cards' }}" 
                                        data-code="{{ set.code }}" 
                                        data-name="{{ set.name }}"
                                        data-has-cards="{{ set.has_cards }}"
                                        data-has-url="{{ 'true' if set.url else 'false' }}">
                                        <td>
                                            <span class="set-code">{{ set.code }}</span>
                                        </td>
                                        <td>
                                            <div>{{ set.name }}</div>
                                            <div class="card-count">
                                                {% if set.has_cards %}
                                                    <i class="fas fa-check-circle text-success"></i> {{ set.actual_cards }} cartes
                                                {% else %}
                                                    <i class="fas fa-exclamation-circle text-danger"></i> Vide
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            {% if set.has_cards %}
                                                <span class="badge bg-success">{{ set.actual_cards }}</span>
                                            {% else %}
                                                <span class="badge bg-danger">0</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <input type="url" 
                                                   class="form-control form-control-sm url-input" 
                                                   data-set-code="{{ set.code }}"
                                                   value="{{ set.url }}" 
                                                   placeholder="https://bulbapedia.bulbagarden.net/wiki/...">
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary btn-sm" onclick="generateBulbapediaUrl('{{ set.code }}', '{{ set.name }}')">
                                                    <i class="fas fa-magic"></i>
                                                </button>
                                                <button class="btn btn-outline-danger btn-sm" onclick="clearUrl('{{ set.code }}')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions en masse (sticky bottom) -->
        <div class="bulk-actions p-3 mt-3">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="d-flex align-items-center">
                        <span class="me-3">
                            <strong id="totalSets">{{ total_sets }}</strong> sets au total
                        </span>
                        <span class="me-3">
                            <span id="setsWithUrls">0</span> avec URLs
                        </span>
                        <span class="me-3">
                            <span id="setsWithCards">0</span> avec cartes
                        </span>
                        <span class="text-muted" id="modifiedCount">0 modifié(s)</span>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <button type="button" class="btn btn-success btn-lg" onclick="saveAllMappings()">
                        <i class="fas fa-save me-2"></i>Sauvegarder Tout
                        <span class="badge bg-light text-dark ms-2" id="saveCount">0</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast pour les notifications -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="saveToast" class="toast" role="alert">
            <div class="toast-header">
                <i class="fas fa-check-circle text-success me-2"></i>
                <strong class="me-auto">Succès</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="toastMessage">
                Mappings sauvegardés avec succès !
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let modifiedSets = new Set();
        
        // Initialiser les compteurs
        document.addEventListener('DOMContentLoaded', function() {
            updateCounters();
            
            // Écouter les changements d'URL
            document.querySelectorAll('.url-input').forEach(input => {
                input.addEventListener('input', function() {
                    modifiedSets.add(this.dataset.setCode);
                    updateCounters();
                });
            });
            
            // Écouter les filtres
            document.getElementById('searchFilter').addEventListener('input', applyFilters);
            document.getElementById('statusFilter').addEventListener('change', applyFilters);
            document.getElementById('eraFilter').addEventListener('change', applyFilters);
        });
        
        function updateCounters() {
            const allInputs = document.querySelectorAll('.url-input');
            const withUrls = Array.from(allInputs).filter(input => input.value.trim()).length;
            const withCards = document.querySelectorAll('.has-cards').length;
            
            document.getElementById('setsWithUrls').textContent = withUrls;
            document.getElementById('setsWithCards').textContent = withCards;
            document.getElementById('modifiedCount').textContent = modifiedSets.size + ' modifié(s)';
            document.getElementById('saveCount').textContent = modifiedSets.size;
        }
        
        function generateBulbapediaUrl(code, name) {
            // Générer une URL Bulbapedia basée sur le nom du set
            const cleanName = name.replace(/[^a-zA-Z0-9\s&]/g, '').trim();
            const urlName = cleanName.replace(/\s+/g, '_').replace(/&/g, '%26');
            const url = `https://bulbapedia.bulbagarden.net/wiki/${urlName}_(TCG)`;
            
            const input = document.querySelector(`input[data-set-code="${code}"]`);
            input.value = url;
            modifiedSets.add(code);
            updateCounters();
        }
        
        function clearUrl(code) {
            const input = document.querySelector(`input[data-set-code="${code}"]`);
            input.value = '';
            modifiedSets.add(code);
            updateCounters();
        }
        
        function fillBulbapediaUrls() {
            if (!confirm('Générer automatiquement les URLs Bulbapedia pour tous les sets vides ?')) return;
            
            document.querySelectorAll('.url-input').forEach(input => {
                if (!input.value.trim()) {
                    const row = input.closest('tr');
                    const code = input.dataset.setCode;
                    const name = row.dataset.name;
                    generateBulbapediaUrl(code, name);
                }
            });
        }
        
        function clearAllUrls() {
            if (!confirm('Vider toutes les URLs ? Cette action ne peut pas être annulée.')) return;
            
            document.querySelectorAll('.url-input').forEach(input => {
                input.value = '';
                modifiedSets.add(input.dataset.setCode);
            });
            updateCounters();
        }
        
        function filterSets() {
            const panel = document.getElementById('filterPanel');
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        }
        
        function applyFilters() {
            const searchTerm = document.getElementById('searchFilter').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const eraFilter = document.getElementById('eraFilter').value;
            
            document.querySelectorAll('.set-row').forEach(row => {
                const code = row.dataset.code.toLowerCase();
                const name = row.dataset.name.toLowerCase();
                const hasCards = row.dataset.hasCards === 'true';
                const hasUrl = row.dataset.hasUrl === 'true';
                
                let show = true;
                
                // Filtre de recherche
                if (searchTerm && !code.includes(searchTerm) && !name.includes(searchTerm)) {
                    show = false;
                }
                
                // Filtre de statut
                if (statusFilter) {
                    switch (statusFilter) {
                        case 'with-cards': show = show && hasCards; break;
                        case 'without-cards': show = show && !hasCards; break;
                        case 'with-url': show = show && hasUrl; break;
                        case 'without-url': show = show && !hasUrl; break;
                    }
                }
                
                // Filtre d'ère
                if (eraFilter && !code.startsWith(eraFilter.toLowerCase())) {
                    show = false;
                }
                
                row.style.display = show ? '' : 'none';
            });
        }
        
        function resetFilters() {
            document.getElementById('searchFilter').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('eraFilter').value = '';
            applyFilters();
        }
        
        function saveAllMappings() {
            const mappings = {};
            
            document.querySelectorAll('.url-input').forEach(input => {
                const code = input.dataset.setCode;
                const url = input.value.trim();
                if (url) {
                    mappings[code] = url;
                }
            });
            
            fetch('/bulk-update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ mappings: mappings })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('toastMessage').textContent = data.message;
                    const toast = new bootstrap.Toast(document.getElementById('saveToast'));
                    toast.show();
                    modifiedSets.clear();
                    updateCounters();
                } else {
                    alert('Erreur: ' + data.error);
                }
            })
            .catch(error => {
                alert('Erreur de sauvegarde: ' + error);
            });
        }
    </script>
</body>
</html>
