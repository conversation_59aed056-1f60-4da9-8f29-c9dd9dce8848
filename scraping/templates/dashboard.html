{% extends "base.html" %}

{% block title %}Dashboard - Pokemon TCG Database{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">🏠 Dashboard</h1>
    <p class="page-subtitle">Overview of your Pokemon TCG collection and system status</p>
</div>

<div class="content-card">
    <h2 style="margin-bottom: 1.5rem; color: #1f2937;">📊 Collection Statistics</h2>
    
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ "{:,}".format(stats.total_cards) }}</div>
            <div class="stat-label">Total Cards</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-number">{{ "{:,}".format(stats.total_sets) }}</div>
            <div class="stat-label">Pokemon Sets</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-number">{{ stats.total_languages }}</div>
            <div class="stat-label">Languages</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-number">{{ stats.english_coverage }}%</div>
            <div class="stat-label">English Coverage</div>
        </div>
    </div>
</div>

<div class="content-card">
    <h2 style="margin-bottom: 1.5rem; color: #1f2937;">🆕 Recently Added Sets</h2>
    
    <div class="table-container">
        <table>
            <thead>
                <tr>
                    <th>Set Code</th>
                    <th>Name</th>
                    <th>Language</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for set_code, name, language in stats.recent_sets %}
                <tr>
                    <td><strong>{{ set_code }}</strong></td>
                    <td>{{ name }}</td>
                    <td><span class="badge">{{ language }}</span></td>
                    <td>
                        <a href="{{ url_for('show_cards', set_code=set_code) }}" class="btn" style="padding: 0.5rem 1rem; font-size: 0.875rem;">
                            View Cards
                        </a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<div class="content-card">
    <h2 style="margin-bottom: 1.5rem; color: #1f2937;">🚀 Quick Actions</h2>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
        <div style="text-align: center; padding: 1.5rem; border: 2px dashed #e5e7eb; border-radius: 12px;">
            <div style="font-size: 3rem; margin-bottom: 1rem;">📚</div>
            <h3 style="margin-bottom: 0.5rem;">Browse Sets</h3>
            <p style="color: #6b7280; margin-bottom: 1rem;">Explore all {{ "{:,}".format(stats.total_sets) }} Pokemon TCG sets</p>
            <a href="{{ url_for('sets') }}" class="btn">View All Sets</a>
        </div>
        
        <div style="text-align: center; padding: 1.5rem; border: 2px dashed #e5e7eb; border-radius: 12px;">
            <div style="font-size: 3rem; margin-bottom: 1rem;">🔍</div>
            <h3 style="margin-bottom: 0.5rem;">Search Cards</h3>
            <p style="color: #6b7280; margin-bottom: 1rem;">Find specific cards across all sets</p>
            <a href="{{ url_for('search') }}" class="btn">Start Searching</a>
        </div>
        
        <div style="text-align: center; padding: 1.5rem; border: 2px dashed #e5e7eb; border-radius: 12px;">
            <div style="font-size: 3rem; margin-bottom: 1rem;">📊</div>
            <h3 style="margin-bottom: 0.5rem;">Data Quality</h3>
            <p style="color: #6b7280; margin-bottom: 1rem;">Monitor collection completeness</p>
            <a href="{{ url_for('quality_dashboard') }}" class="btn">View Quality</a>
        </div>
        
        <div style="text-align: center; padding: 1.5rem; border: 2px dashed #e5e7eb; border-radius: 12px;">
            <div style="font-size: 3rem; margin-bottom: 1rem;">🕷️</div>
            <h3 style="margin-bottom: 0.5rem;">Scraping Tools</h3>
            <p style="color: #6b7280; margin-bottom: 1rem;">Manage data collection</p>
            <a href="{{ url_for('scraping_dashboard') }}" class="btn">Open Tools</a>
        </div>
    </div>
</div>

<div class="content-card">
    <h2 style="margin-bottom: 1.5rem; color: #1f2937;">💡 System Status</h2>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
        <div style="padding: 1rem; background: #d1fae5; border-radius: 8px; border-left: 4px solid #10b981;">
            <div style="font-weight: 600; color: #065f46;">Database Connection</div>
            <div style="color: #047857; font-size: 0.875rem;">✅ Connected and operational</div>
        </div>
        
        <div style="padding: 1rem; background: #dbeafe; border-radius: 8px; border-left: 4px solid #3b82f6;">
            <div style="font-weight: 600; color: #1e40af;">Enhanced Scraping</div>
            <div style="color: #1d4ed8; font-size: 0.875rem;">🚀 Ready for data collection</div>
        </div>
        
        <div style="padding: 1rem; background: #fef3c7; border-radius: 8px; border-left: 4px solid #f59e0b;">
            <div style="font-weight: 600; color: #92400e;">Data Quality</div>
            <div style="color: #b45309; font-size: 0.875rem;">⚠️ {{ stats.english_coverage }}% English coverage</div>
        </div>
        
        <div style="padding: 1rem; background: #e0e7ff; border-radius: 8px; border-left: 4px solid #8b5cf6;">
            <div style="font-weight: 600; color: #5b21b6;">Web Interface</div>
            <div style="color: #7c3aed; font-size: 0.875rem;">🌐 Online and responsive</div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add some animation to the stats cards
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
    
    // Animate the stat numbers
    const statNumbers = document.querySelectorAll('.stat-number');
    statNumbers.forEach(number => {
        const finalValue = parseInt(number.textContent.replace(/,/g, ''));
        if (!isNaN(finalValue)) {
            let currentValue = 0;
            const increment = finalValue / 50;
            const timer = setInterval(() => {
                currentValue += increment;
                if (currentValue >= finalValue) {
                    currentValue = finalValue;
                    clearInterval(timer);
                }
                number.textContent = Math.floor(currentValue).toLocaleString();
            }, 30);
        }
    });
});
</script>
{% endblock %}
