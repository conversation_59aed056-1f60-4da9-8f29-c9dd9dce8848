{% extends "base.html" %}

{% block title %}Data Quality - Pokemon TCG Database{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">📊 Data Quality Dashboard</h1>
    <p class="page-subtitle">Monitor the completeness and quality of your Pokemon TCG collection</p>
</div>

<div class="content-card">
    <h2 style="margin-bottom: 1.5rem; color: #1f2937;">🎯 Quality Overview</h2>
    
    <div class="stats-grid">
        <div class="stat-card" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
            <div class="stat-number">{{ stats.completeness }}%</div>
            <div class="stat-label">Overall Completeness</div>
        </div>
        
        <div class="stat-card" style="background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);">
            <div class="stat-number">{{ "{:,}".format(stats.total_cards) }}</div>
            <div class="stat-label">Total Cards</div>
        </div>
        
        <div class="stat-card" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
            <div class="stat-number">{{ "{:,}".format(stats.missing_english) }}</div>
            <div class="stat-label">Missing English Names</div>
        </div>
        
        <div class="stat-card" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);">
            <div class="stat-number">{{ "{:,}".format(stats.missing_rarity + stats.missing_numbers) }}</div>
            <div class="stat-label">Missing Metadata</div>
        </div>
    </div>
</div>

<div class="content-card">
    <h2 style="margin-bottom: 1.5rem; color: #1f2937;">📈 Data Completeness Breakdown</h2>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
        <!-- English Names Progress -->
        <div>
            <h3 style="color: #374151; margin-bottom: 1rem;">🇬🇧 English Names</h3>
            <div style="background: #f3f4f6; border-radius: 8px; overflow: hidden; margin-bottom: 0.5rem;">
                {% set english_percentage = ((stats.total_cards - stats.missing_english) / stats.total_cards * 100) if stats.total_cards > 0 else 0 %}
                <div style="background: #10b981; height: 20px; width: {{ english_percentage }}%; transition: width 0.5s ease;"></div>
            </div>
            <div style="display: flex; justify-content: space-between; font-size: 0.875rem; color: #6b7280;">
                <span>{{ "{:,}".format(stats.total_cards - stats.missing_english) }} / {{ "{:,}".format(stats.total_cards) }}</span>
                <span>{{ "%.1f"|format(english_percentage) }}%</span>
            </div>
        </div>
        
        <!-- Rarity Information Progress -->
        <div>
            <h3 style="color: #374151; margin-bottom: 1rem;">⭐ Rarity Information</h3>
            <div style="background: #f3f4f6; border-radius: 8px; overflow: hidden; margin-bottom: 0.5rem;">
                {% set rarity_percentage = ((stats.total_cards - stats.missing_rarity) / stats.total_cards * 100) if stats.total_cards > 0 else 0 %}
                <div style="background: #3b82f6; height: 20px; width: {{ rarity_percentage }}%; transition: width 0.5s ease;"></div>
            </div>
            <div style="display: flex; justify-content: space-between; font-size: 0.875rem; color: #6b7280;">
                <span>{{ "{:,}".format(stats.total_cards - stats.missing_rarity) }} / {{ "{:,}".format(stats.total_cards) }}</span>
                <span>{{ "%.1f"|format(rarity_percentage) }}%</span>
            </div>
        </div>
        
        <!-- Card Numbers Progress -->
        <div>
            <h3 style="color: #374151; margin-bottom: 1rem;">🔢 Card Numbers</h3>
            <div style="background: #f3f4f6; border-radius: 8px; overflow: hidden; margin-bottom: 0.5rem;">
                {% set numbers_percentage = ((stats.total_cards - stats.missing_numbers) / stats.total_cards * 100) if stats.total_cards > 0 else 0 %}
                <div style="background: #8b5cf6; height: 20px; width: {{ numbers_percentage }}%; transition: width 0.5s ease;"></div>
            </div>
            <div style="display: flex; justify-content: space-between; font-size: 0.875rem; color: #6b7280;">
                <span>{{ "{:,}".format(stats.total_cards - stats.missing_numbers) }} / {{ "{:,}".format(stats.total_cards) }}</span>
                <span>{{ "%.1f"|format(numbers_percentage) }}%</span>
            </div>
        </div>
    </div>
</div>

<div class="content-card">
    <h2 style="margin-bottom: 1.5rem; color: #1f2937;">🌍 Language Distribution</h2>
    
    <div class="table-container">
        <table>
            <thead>
                <tr>
                    <th>Language</th>
                    <th>Sets Count</th>
                    <th>Percentage</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                {% set total_sets = stats.language_stats|sum(attribute=1) %}
                {% for language, count in stats.language_stats %}
                <tr>
                    <td>
                        <span class="badge">{{ language|title }}</span>
                    </td>
                    <td>{{ "{:,}".format(count) }}</td>
                    <td>
                        {% set percentage = (count / total_sets * 100) if total_sets > 0 else 0 %}
                        <div style="display: flex; align-items: center; gap: 0.5rem;">
                            <div style="background: #f3f4f6; border-radius: 4px; overflow: hidden; width: 100px; height: 8px;">
                                <div style="background: #4f46e5; height: 100%; width: {{ percentage }}%; transition: width 0.5s ease;"></div>
                            </div>
                            <span style="font-size: 0.875rem; color: #6b7280;">{{ "%.1f"|format(percentage) }}%</span>
                        </div>
                    </td>
                    <td>
                        {% if percentage > 20 %}
                            <span class="badge badge-success">High Coverage</span>
                        {% elif percentage > 5 %}
                            <span class="badge badge-warning">Medium Coverage</span>
                        {% else %}
                            <span class="badge badge-error">Low Coverage</span>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<div class="content-card">
    <h2 style="margin-bottom: 1.5rem; color: #1f2937;">🚀 Quality Improvement Actions</h2>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
        <div style="padding: 1.5rem; border: 2px solid #e5e7eb; border-radius: 12px; text-align: center;">
            <div style="font-size: 3rem; margin-bottom: 1rem;">🇬🇧</div>
            <h3 style="margin-bottom: 0.5rem;">Improve English Coverage</h3>
            <p style="color: #6b7280; margin-bottom: 1rem; font-size: 0.875rem;">
                {{ "{:,}".format(stats.missing_english) }} cards missing English names
            </p>
            <a href="{{ url_for('scraping_dashboard') }}" class="btn">Run Enhanced Scraping</a>
        </div>
        
        <div style="padding: 1.5rem; border: 2px solid #e5e7eb; border-radius: 12px; text-align: center;">
            <div style="font-size: 3rem; margin-bottom: 1rem;">⭐</div>
            <h3 style="margin-bottom: 0.5rem;">Add Rarity Data</h3>
            <p style="color: #6b7280; margin-bottom: 1rem; font-size: 0.875rem;">
                {{ "{:,}".format(stats.missing_rarity) }} cards missing rarity information
            </p>
            <a href="{{ url_for('scraping_dashboard') }}" class="btn">Update Metadata</a>
        </div>
        
        <div style="padding: 1.5rem; border: 2px solid #e5e7eb; border-radius: 12px; text-align: center;">
            <div style="font-size: 3rem; margin-bottom: 1rem;">🔢</div>
            <h3 style="margin-bottom: 0.5rem;">Fix Card Numbers</h3>
            <p style="color: #6b7280; margin-bottom: 1rem; font-size: 0.875rem;">
                {{ "{:,}".format(stats.missing_numbers) }} cards missing card numbers
            </p>
            <a href="{{ url_for('scraping_dashboard') }}" class="btn">Validate Numbers</a>
        </div>
        
        <div style="padding: 1.5rem; border: 2px solid #e5e7eb; border-radius: 12px; text-align: center;">
            <div style="font-size: 3rem; margin-bottom: 1rem;">🔍</div>
            <h3 style="margin-bottom: 0.5rem;">Quality Analysis</h3>
            <p style="color: #6b7280; margin-bottom: 1rem; font-size: 0.875rem;">
                Run comprehensive data validation
            </p>
            <a href="{{ url_for('search') }}?q=" class="btn btn-secondary">Search & Verify</a>
        </div>
    </div>
</div>

<div class="content-card">
    <h2 style="margin-bottom: 1.5rem; color: #1f2937;">📋 Quality Recommendations</h2>
    
    <div style="display: flex; flex-direction: column; gap: 1rem;">
        {% if stats.missing_english > 1000 %}
        <div style="padding: 1rem; background: #fef3c7; border-radius: 8px; border-left: 4px solid #f59e0b;">
            <div style="font-weight: 600; color: #92400e;">⚠️ High Priority: English Names</div>
            <div style="color: #b45309; font-size: 0.875rem;">
                Over {{ "{:,}".format(stats.missing_english) }} cards are missing English names. Consider running the enhanced Bulbapedia scraper.
            </div>
        </div>
        {% endif %}
        
        {% if stats.missing_rarity > 500 %}
        <div style="padding: 1rem; background: #dbeafe; border-radius: 8px; border-left: 4px solid #3b82f6;">
            <div style="font-weight: 600; color: #1e40af;">💡 Medium Priority: Rarity Information</div>
            <div style="color: #1d4ed8; font-size: 0.875rem;">
                {{ "{:,}".format(stats.missing_rarity) }} cards lack rarity information. This affects collection value assessment.
            </div>
        </div>
        {% endif %}
        
        {% if stats.completeness > 80 %}
        <div style="padding: 1rem; background: #d1fae5; border-radius: 8px; border-left: 4px solid #10b981;">
            <div style="font-weight: 600; color: #065f46;">✅ Excellent: High Data Quality</div>
            <div style="color: #047857; font-size: 0.875rem;">
                Your collection has {{ stats.completeness }}% completeness. Great work maintaining data quality!
            </div>
        </div>
        {% endif %}
        
        <div style="padding: 1rem; background: #e0e7ff; border-radius: 8px; border-left: 4px solid #8b5cf6;">
            <div style="font-weight: 600; color: #5b21b6;">🚀 Automation Tip</div>
            <div style="color: #7c3aed; font-size: 0.875rem;">
                Use the enhanced scraping system to automatically improve data quality with validation and quality scoring.
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animate progress bars
    const progressBars = document.querySelectorAll('[style*="width:"]');
    progressBars.forEach(bar => {
        const width = bar.style.width;
        bar.style.width = '0%';
        setTimeout(() => {
            bar.style.width = width;
        }, 500);
    });
    
    // Add tooltips to quality metrics
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(-4px) scale(1)';
        });
    });
});
</script>
{% endblock %}
