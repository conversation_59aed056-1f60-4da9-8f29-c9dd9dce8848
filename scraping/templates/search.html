{% extends "base.html" %}

{% block title %}Search Cards - Pokemon TCG Database{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">🔍 Search Pokemon Cards</h1>
    <p class="page-subtitle">Find specific cards across all sets and languages</p>
</div>

<div class="content-card">
    <h2 style="margin-bottom: 1.5rem; color: #1f2937;">Search Filters</h2>
    
    <form method="get" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 2rem;">
        <div class="form-group">
            <label for="q">Card Name or Number:</label>
            <input type="text" id="q" name="q" value="{{ query }}" placeholder="Enter card name or number..." autofocus>
        </div>
        
        <div class="form-group">
            <label for="language">Language:</label>
            <select name="language" id="language">
                <option value="">-- All Languages --</option>
                {% for lang in languages %}
                    <option value="{{ lang }}" {% if lang == selected_language %}selected{% endif %}>
                        {{ lang|title }}
                    </option>
                {% endfor %}
            </select>
        </div>
        
        <div class="form-group">
            <label for="set">Set:</label>
            <select name="set" id="set">
                <option value="">-- All Sets --</option>
                {% for set_code, set_name in sets %}
                    <option value="{{ set_code }}" {% if set_code == selected_set %}selected{% endif %}>
                        {{ set_code }} - {{ set_name[:50] }}{% if set_name|length > 50 %}...{% endif %}
                    </option>
                {% endfor %}
            </select>
        </div>
        
        <div class="form-group" style="display: flex; align-items: end;">
            <button type="submit" class="btn" style="width: 100%;">
                🔍 Search Cards
            </button>
        </div>
    </form>
</div>

{% if query %}
<div class="content-card">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem; flex-wrap: wrap; gap: 1rem;">
        <h2 style="color: #1f2937; margin: 0;">
            Search Results for "{{ query }}"
        </h2>
        <span class="badge" style="font-size: 1rem; padding: 0.5rem 1rem;">
            {{ results|length }} cards found
        </span>
    </div>

    {% if results %}
    <div class="table-container">
        <table>
            <thead>
                <tr>
                    <th>Card #</th>
                    <th>Thai Name</th>
                    <th>English Name</th>
                    <th>Set</th>
                    <th>Rarity</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for card in results %}
                <tr>
                    <td>
                        <strong style="color: #4f46e5;">{{ card.card_number or '-' }}</strong>
                    </td>
                    <td>{{ card.name_th or '-' }}</td>
                    <td>{{ card.name_en or '-' }}</td>
                    <td>
                        <div>
                            <strong>{{ card.set_code }}</strong>
                            <div style="font-size: 0.875rem; color: #6b7280;">
                                {{ card.set_name[:40] }}{% if card.set_name|length > 40 %}...{% endif %}
                            </div>
                        </div>
                    </td>
                    <td>
                        {% if card.rarity %}
                            <span class="badge">{{ card.rarity }}</span>
                        {% else %}
                            <span style="color: #9ca3af;">-</span>
                        {% endif %}
                    </td>
                    <td>
                        <a href="{{ url_for('card_detail', card_id=card.id) }}" class="btn" style="padding: 0.5rem 1rem; font-size: 0.875rem;">
                            View Details
                        </a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div style="text-align: center; padding: 3rem; color: #6b7280;">
        <div style="font-size: 4rem; margin-bottom: 1rem;">🔍</div>
        <h3>No cards found</h3>
        <p>Try adjusting your search terms or filters.</p>
        <div style="margin-top: 1.5rem;">
            <a href="{{ url_for('search') }}" class="btn btn-secondary">Clear Search</a>
        </div>
    </div>
    {% endif %}
</div>
{% endif %}

<div class="content-card">
    <h3 style="color: #1f2937; margin-bottom: 1rem;">💡 Search Tips</h3>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
        <div style="padding: 1rem; background: #f8fafc; border-radius: 8px; border-left: 4px solid #4f46e5;">
            <h4 style="color: #4f46e5; margin-bottom: 0.5rem;">🎯 Card Names</h4>
            <p style="font-size: 0.875rem; color: #6b7280; margin: 0;">
                Search by Pokemon name in Thai or English (e.g., "Pikachu", "ปิกาจู")
            </p>
        </div>
        
        <div style="padding: 1rem; background: #f8fafc; border-radius: 8px; border-left: 4px solid #10b981;">
            <h4 style="color: #10b981; margin-bottom: 0.5rem;">🔢 Card Numbers</h4>
            <p style="font-size: 0.875rem; color: #6b7280; margin: 0;">
                Search by card number (e.g., "001", "025/102")
            </p>
        </div>
        
        <div style="padding: 1rem; background: #f8fafc; border-radius: 8px; border-left: 4px solid #f59e0b;">
            <h4 style="color: #f59e0b; margin-bottom: 0.5rem;">🎨 Filters</h4>
            <p style="font-size: 0.875rem; color: #6b7280; margin: 0;">
                Use language and set filters to narrow down results
            </p>
        </div>
        
        <div style="padding: 1rem; background: #f8fafc; border-radius: 8px; border-left: 4px solid #8b5cf6;">
            <h4 style="color: #8b5cf6; margin-bottom: 0.5rem;">⚡ Quick Search</h4>
            <p style="font-size: 0.875rem; color: #6b7280; margin: 0;">
                Results are limited to 100 cards for performance
            </p>
        </div>
    </div>
</div>

{% if not query %}
<div class="content-card">
    <h3 style="color: #1f2937; margin-bottom: 1rem;">🚀 Quick Actions</h3>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
        <a href="{{ url_for('search') }}?q=Pikachu" class="btn" style="text-align: center; padding: 1rem;">
            Search "Pikachu"
        </a>
        <a href="{{ url_for('search') }}?q=001" class="btn" style="text-align: center; padding: 1rem;">
            Search Card #001
        </a>
        <a href="{{ url_for('search') }}?language=th" class="btn" style="text-align: center; padding: 1rem;">
            Browse Thai Cards
        </a>
        <a href="{{ url_for('search') }}?language=en" class="btn" style="text-align: center; padding: 1rem;">
            Browse English Cards
        </a>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit form when filters change (except for the main search input)
    const languageSelect = document.getElementById('language');
    const setSelect = document.getElementById('set');
    
    [languageSelect, setSelect].forEach(select => {
        select.addEventListener('change', function() {
            // Only auto-submit if there's already a search query
            const queryInput = document.getElementById('q');
            if (queryInput.value.trim()) {
                this.form.submit();
            }
        });
    });
    
    // Add search suggestions (simple implementation)
    const searchInput = document.getElementById('q');
    searchInput.addEventListener('input', function() {
        // Could add autocomplete functionality here
        // For now, just provide visual feedback
        if (this.value.length > 0) {
            this.style.borderColor = '#4f46e5';
        } else {
            this.style.borderColor = '#e5e7eb';
        }
    });
    
    // Highlight search terms in results
    const query = "{{ query }}";
    if (query) {
        const cells = document.querySelectorAll('td');
        cells.forEach(cell => {
            if (cell.textContent.toLowerCase().includes(query.toLowerCase())) {
                cell.innerHTML = cell.innerHTML.replace(
                    new RegExp(query, 'gi'),
                    '<mark style="background: #fef3c7; padding: 0.125rem 0.25rem; border-radius: 4px;">$&</mark>'
                );
            }
        });
    }
});
</script>
{% endblock %}
