<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title><PERSON><PERSON><PERSON> de la carte {{ card.name_th }}</title>
</head>
<body>
    <h1><PERSON><PERSON><PERSON> de la carte</h1>
    <a href="{{ url_for('show_cards', set_code=card.card_number.split('/')[1] if '/' in card.card_number else '') }}">&larr; Retour au set</a>
    <table border="1" cellpadding="5">
        <tr><th>Nom (TH)</th><td>{{ card.name_th }}</td></tr>
        <tr><th>Nom (EN)</th><td>{{ card.name_en or '' }}</td></tr>
        <tr><th>Numéro</th><td>{{ card.card_number }}</td></tr>
        <tr><th>Image</th><td>{% if card.image_url %}<img src="/images/{{ card.image_url.split('images/')[-1] }}" alt="img" width="200">{% endif %}</td></tr>
        <tr><th>Rareté</th><td>{{ card.rarity or '' }}</td></tr>
        <tr><th>Type</th><td>{{ card.card_type or '' }}</td></tr>
        <tr><th>HP</th><td>{{ card.hp or '' }}</td></tr>
        <tr><th>Illustrateur</th><td>{{ card.illustrator_id or '' }}</td></tr>
        <tr><th>URL carte</th><td>{% if card.card_url %}<a href="{{ card.card_url }}" target="_blank">Lien</a>{% endif %}</td></tr>
    </table>
</body>
</html> 