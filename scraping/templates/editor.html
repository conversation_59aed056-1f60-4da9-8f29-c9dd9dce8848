<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Éditeur de mapping code → URL</title>
    <style>
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ccc; padding: 6px; }
        th { background: #eee; }
        input[type=text] { width: 95%; }
        .actions { white-space: nowrap; }
    </style>
</head>
<body>
    <h1>Éditeur de mapping code → URL Bulbapedia</h1>
    <table id="mapping-table">
        <thead>
            <tr>
                <th>Code</th>
                <th>URL Bulbapedia</th>
                <th class="actions">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for code, url in mapping.items() %}
            <tr data-code="{{ code }}">
                <td>{{ code }}</td>
                <td><input type="text" value="{{ url or '' }}" /></td>
                <td class="actions">
                    <button onclick="saveRow(this)">💾</button>
                    <button onclick="deleteRow(this)">🗑️</button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot>
            <tr>
                <td><input type="text" id="new-code" placeholder="Nouveau code" /></td>
                <td><input type="text" id="new-url" placeholder="Nouvelle URL" /></td>
                <td class="actions"><button onclick="addRow()">➕ Ajouter</button></td>
            </tr>
        </tfoot>
    </table>
    <script>
    function saveRow(btn) {
        const tr = btn.closest('tr');
        const code = tr.dataset.code;
        const url = tr.querySelector('input').value;
        fetch('/update', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ code, url })
        }).then(r => r.json()).then(data => {
            if (!data.success) alert('Erreur: ' + data.error);
        });
    }
    function deleteRow(btn) {
        const tr = btn.closest('tr');
        const code = tr.dataset.code;
        if (!confirm('Supprimer ' + code + ' ?')) return;
        fetch('/delete', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ code })
        }).then(r => r.json()).then(data => {
            if (data.success) tr.remove();
            else alert('Erreur: ' + data.error);
        });
    }
    function addRow() {
        const code = document.getElementById('new-code').value.trim();
        const url = document.getElementById('new-url').value.trim();
        if (!code) { alert('Code requis'); return; }
        fetch('/add', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ code, url })
        }).then(r => r.json()).then(data => {
            if (data.success) location.reload();
            else alert('Erreur: ' + data.error);
        });
    }
    </script>
</body>
</html> 