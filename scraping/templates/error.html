<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ error_title }} - Pokemon TCG Database</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-cards-blank me-2"></i>Pokemon TCG Database
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/sets">
                    <i class="fas fa-layer-group me-1"></i>Sets
                </a>
                <a class="nav-link" href="/search">
                    <i class="fas fa-search me-1"></i>Recherche
                </a>
                <a class="nav-link" href="/quality">
                    <i class="fas fa-chart-bar me-1"></i>Qualité
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card border-danger">
                    <div class="card-header bg-danger text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>{{ error_title }}
                        </h4>
                    </div>
                    <div class="card-body text-center">
                        <div class="mb-4">
                            <i class="fas fa-exclamation-circle text-danger" style="font-size: 4rem;"></i>
                        </div>
                        <h5 class="card-title">{{ error_message }}</h5>
                        <p class="card-text text-muted">
                            Le set que vous recherchez n'existe pas ou n'a pas encore été ajouté à la base de données.
                        </p>
                        <div class="mt-4">
                            <a href="/sets" class="btn btn-primary me-2">
                                <i class="fas fa-layer-group me-1"></i>Voir tous les sets
                            </a>
                            <a href="/search" class="btn btn-outline-secondary">
                                <i class="fas fa-search me-1"></i>Rechercher
                            </a>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-lightbulb me-2"></i>Suggestions
                            </h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Vérifiez l'orthographe du nom du set
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Consultez la liste complète des sets disponibles
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Utilisez la fonction de recherche pour trouver des cartes spécifiques
                                </li>
                                <li>
                                    <i class="fas fa-check text-success me-2"></i>
                                    Le set pourrait être en cours d'ajout à la base de données
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
