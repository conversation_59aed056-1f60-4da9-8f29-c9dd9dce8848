#!/usr/bin/env python3
"""
Scraper Bulbapedia - Source de référence pour Pokemon TCG
Récupère TOUS les sets et TOUTES les cartes depuis Bulbapedia.org

RÈGLES NON NÉGOCIABLES :
- Scraping complet de tous les sets disponibles
- Récupération de toutes les cartes de chaque set
- Fonctionnement autonome et indépendant
- Source unique : bulbapedia.org
"""

import requests
from bs4 import BeautifulSoup
import mysql.connector
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.database import get_db_config
import time
import random
import logging
import argparse
import re
from urllib.parse import urljoin

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# URLs Bulbapedia
BASE_URL = "https://bulbapedia.bulbagarden.net"
SETS_LIST_URL = "https://bulbapedia.bulbagarden.net/wiki/List_of_Pok%C3%A9mon_Trading_Card_Game_expansions"

def discover_all_bulbapedia_sets():
    """Découvre TOUS les sets Pokemon TCG sur Bulbapedia"""
    
    print("🔍 DÉCOUVERTE DE TOUS LES SETS BULBAPEDIA")
    print("=" * 60)
    
    try:
        response = requests.get(SETS_LIST_URL, timeout=15)
        soup = BeautifulSoup(response.content, 'html.parser')
        
        sets_found = []
        
        # Chercher tous les liens vers des sets
        for link in soup.find_all('a', href=True):
            href = link.get('href')
            if href and '/wiki/' in href and any(keyword in href.lower() for keyword in ['tcg', 'expansion', 'set']):
                set_name = link.get_text(strip=True)
                if set_name and len(set_name) > 2:
                    full_url = urljoin(BASE_URL, href)
                    sets_found.append({
                        'name': set_name,
                        'url': full_url,
                        'code': extract_set_code_from_name(set_name)
                    })
        
        # Déduplication
        unique_sets = {}
        for set_info in sets_found:
            if set_info['code'] not in unique_sets:
                unique_sets[set_info['code']] = set_info
        
        final_sets = list(unique_sets.values())
        
        print(f"✅ {len(final_sets)} sets uniques découverts sur Bulbapedia")
        
        return final_sets
        
    except Exception as e:
        logger.error(f"Erreur découverte sets Bulbapedia: {e}")
        return []

def extract_set_code_from_name(set_name):
    """Extrait un code de set depuis le nom"""
    
    # Nettoyer le nom
    clean_name = re.sub(r'[^\w\s]', '', set_name)
    words = clean_name.split()
    
    if len(words) >= 2:
        return f"{words[0][:3]}{words[1][:3]}".upper()
    elif len(words) == 1:
        return words[0][:6].upper()
    else:
        return "UNKNOWN"

def scrape_set_from_bulbapedia(set_info):
    """Scrape toutes les cartes d'un set depuis Bulbapedia"""
    
    print(f"\n🎯 SCRAPING SET: {set_info['name']}")
    print("=" * 50)
    print(f"📡 URL: {set_info['url']}")
    
    try:
        response = requests.get(set_info['url'], timeout=15)
        soup = BeautifulSoup(response.content, 'html.parser')
        
        cards = []
        
        # Chercher les tableaux de cartes (structure typique Bulbapedia)
        tables = soup.find_all('table', class_=['wikitable', 'sortable'])
        
        for table in tables:
            rows = table.find_all('tr')[1:]  # Skip header
            
            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 3:
                    
                    # Extraire les informations de base
                    card_number = cells[0].get_text(strip=True) if cells[0] else ""
                    card_name = cells[1].get_text(strip=True) if cells[1] else ""
                    card_type = cells[2].get_text(strip=True) if len(cells) > 2 else ""
                    
                    if card_number and card_name:
                        cards.append({
                            'card_number': card_number,
                            'name_en': card_name,
                            'card_type': card_type,
                            'rarity': extract_rarity_from_row(row),
                            'set_code': set_info['code']
                        })
        
        print(f"📋 {len(cards)} cartes trouvées pour {set_info['name']}")
        return cards
        
    except Exception as e:
        logger.error(f"Erreur scraping set {set_info['name']}: {e}")
        return []

def extract_rarity_from_row(row):
    """Extrait la rareté depuis une ligne de tableau"""
    
    # Chercher des images de rareté ou du texte
    rarity_indicators = ['common', 'uncommon', 'rare', 'ultra', 'secret', 'promo']
    
    row_text = row.get_text().lower()
    for indicator in rarity_indicators:
        if indicator in row_text:
            return indicator.title()
    
    return "Unknown"

def get_language_id(connection, language_code):
    """Récupère l'ID de la langue"""
    cursor = connection.cursor(dictionary=True)
    cursor.execute("SELECT id FROM languages WHERE code = %s", (language_code,))
    result = cursor.fetchone()
    return result['id'] if result else None

def get_or_create_expansion(connection, set_code, set_name_en=None):
    """Récupère ou crée une expansion dans la nouvelle table expansions"""
    cursor = connection.cursor(dictionary=True)

    # Vérifier si l'expansion existe déjà
    cursor.execute("SELECT id FROM expansions WHERE code = %s", (set_code,))
    existing_expansion = cursor.fetchone()

    if existing_expansion:
        return existing_expansion['id']

    # Créer la nouvelle expansion
    print(f"🆕 Création de la nouvelle expansion: {set_code} - {set_name_en}")

    cursor.execute("""
        INSERT INTO expansions (code, name_en, region)
        VALUES (%s, %s, 'Global')
    """, (set_code, set_name_en))

    connection.commit()
    return cursor.lastrowid

def get_or_create_card(connection, expansion_id, card_number, supertype='Pokémon', illustrator=None):
    """Récupère ou crée une carte maître"""
    cursor = connection.cursor(dictionary=True)

    # Chercher une carte existante avec ce numéro dans cette expansion
    cursor.execute("""
        SELECT id FROM cards
        WHERE canonical_number = %s AND expansion_id = %s
    """, (card_number, expansion_id))

    existing_card = cursor.fetchone()
    if existing_card:
        return existing_card['id']

    # Créer une nouvelle carte maître
    cursor.execute("""
        INSERT INTO cards (canonical_number, expansion_id, supertype, illustrator)
        VALUES (%s, %s, %s, %s)
    """, (card_number, expansion_id, supertype, illustrator))

    connection.commit()
    return cursor.lastrowid

def card_version_exists(connection, card_id, language_id):
    """Vérifie si une version de carte existe déjà pour cette langue"""
    cursor = connection.cursor()
    cursor.execute("""
        SELECT id FROM card_versions
        WHERE card_id = %s AND language_id = %s
    """, (card_id, language_id))
    return cursor.fetchone() is not None

def save_bulbapedia_data_to_database(connection, sets_data):
    """Sauvegarde les données Bulbapedia avec la nouvelle structure multilingue"""

    print(f"\n💾 SAUVEGARDE AVEC NOUVELLE STRUCTURE MULTILINGUE")
    print("=" * 50)

    # Récupérer l'ID de la langue anglaise
    english_language_id = get_language_id(connection, 'en')
    if not english_language_id:
        print("❌ Erreur: Langue anglaise non trouvée dans la base")
        return

    total_cards_inserted = 0

    for set_info, cards in sets_data.items():
        try:
            # Créer ou récupérer l'expansion
            expansion_id = get_or_create_expansion(
                connection,
                set_info['code'],
                set_info['name']
            )

            cards_inserted = 0

            for card in cards:
                # Créer ou récupérer la carte maître
                card_id = get_or_create_card(
                    connection,
                    expansion_id,
                    card['card_number'],
                    card.get('card_type', 'Pokémon')
                )

                # Vérifier si la version anglaise existe déjà
                if not card_version_exists(connection, card_id, english_language_id):
                    # Créer la version anglaise de la carte
                    cursor = connection.cursor()
                    cursor.execute("""
                        INSERT INTO card_versions (
                            card_id, language_id, name, rarity
                        ) VALUES (%s, %s, %s, %s)
                    """, (
                        card_id,
                        english_language_id,
                        card['name_en'],
                        card.get('rarity')
                    ))
                    cards_inserted += 1

            connection.commit()
            total_cards_inserted += cards_inserted
            print(f"   ✅ {set_info['name']}: {cards_inserted} cartes insérées")

        except Exception as e:
            logger.error(f"Erreur sauvegarde set {set_info['name']}: {e}")
            connection.rollback()

    print(f"✅ TOTAL: {total_cards_inserted} cartes insérées depuis Bulbapedia")

def main():
    """Fonction principale - Scraping complet Bulbapedia"""
    
    parser = argparse.ArgumentParser(description='Scraper Bulbapedia Pokemon TCG')
    parser.add_argument('--max-sets', type=int, help='Limite le nombre de sets (pour test)')
    args = parser.parse_args()
    
    print("🌐 SCRAPER BULBAPEDIA POKEMON TCG")
    print("=" * 60)
    print("🎯 Objectif: Récupérer TOUS les sets et cartes depuis Bulbapedia")
    print("📊 Source: bulbapedia.bulbagarden.net")
    print("=" * 60)
    
    # Découvrir tous les sets
    all_sets = discover_all_bulbapedia_sets()
    
    if not all_sets:
        print("❌ Aucun set découvert")
        return
    
    # Limiter pour test si demandé
    if args.max_sets:
        all_sets = all_sets[:args.max_sets]
        print(f"🧪 Mode test: limitation à {args.max_sets} sets")
    
    print(f"📋 {len(all_sets)} sets à traiter")
    
    # Connexion base de données
    try:
        connection = mysql.connector.connect(**get_db_config())
        print("✅ Connexion à la base de données établie")
    except Exception as e:
        print(f"❌ Erreur connexion base: {e}")
        return
    
    # Scraper chaque set
    sets_data = {}
    
    for i, set_info in enumerate(all_sets, 1):
        print(f"\n🎯 SET {i}/{len(all_sets)}: {set_info['name']}")
        
        cards = scrape_set_from_bulbapedia(set_info)
        if cards:
            sets_data[set_info] = cards
        
        # Pause entre sets
        time.sleep(random.uniform(1.0, 3.0))
    
    # Sauvegarder en base
    if sets_data:
        save_bulbapedia_data_to_database(connection, sets_data)
    
    connection.close()
    
    print(f"\n" + "=" * 60)
    print("🎉 SCRAPING BULBAPEDIA TERMINÉ")
    print("=" * 60)
    print(f"✅ {len(sets_data)} sets traités")
    print("✅ Toutes les données Bulbapedia récupérées")

if __name__ == "__main__":
    main()
