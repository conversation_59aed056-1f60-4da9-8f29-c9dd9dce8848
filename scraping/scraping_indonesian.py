#!/usr/bin/env python3
"""
Scraper pour récupérer les noms indonésiens des cartes Pokemon TCG
depuis le site officiel indonésien : https://asia.pokemon-card.com/id/card-search/list/

Basé sur scraping_thai.py avec adaptations pour l'indonésien.
"""

import requests
from bs4 import BeautifulSoup
import mysql.connector
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.database import get_db_config
import time
import random
import logging
import argparse
import os
import re
from urllib.parse import urljoin, urlparse

# Configuration du logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

# URLs du site indonésien
BASE_URL = "https://asia.pokemon-card.com"
LIST_URL = "https://asia.pokemon-card.com/id/card-search/list/?expansionCodes="
PROGRESS_FILE = "scraping_indonesian_progress.txt"

# DÉCOUVERTE AUTOMATIQUE DE TOUS LES SETS DANS L'ORDRE DU SITE !
def discover_all_indonesian_sets():
    """Découvre automatiquement TOUS les sets disponibles sur le site indonésien DANS L'ORDRE DU SITE"""
    print("🔍 DÉCOUVERTE AUTOMATIQUE DE TOUS LES SETS INDONÉSIENS...")
    print("📋 Analyse de la structure du site indonésien...")

    # Méthode 1: Essayer de découvrir via les URLs de sets connus
    print("🔍 Méthode 1: Test des sets récents...")

    # Liste des sets récents à tester (ordre inverse = plus récents en premier)
    test_sets = [
        'SV10s', 'SV9s', 'SV8s', 'SV8a', 'SVM', 'SV7s', 'SV6s', 'SV5s', 'SV4s', 'SV4a',
        'SV3s', 'SV3a', 'SV2s', 'SV2a', 'SV1s', 'SV1a',
        'S12a', 'S12', 'S11a', 'S11', 'S10a', 'S10b', 'S10', 'S9a', 'S9', 'S8b', 'S8a', 'S8',
        'S7R', 'S7D', 'S6a', 'S6K', 'S6H', 'S5a', 'S5R', 'S5I', 'S4a', 'S4', 'S3a', 'S3'
    ]

    available_sets = []

    for set_code in test_sets:
        test_url = f"https://asia.pokemon-card.com/id/card-search/list/?expansionCodes={set_code}"
        print(f"   🔍 Test {set_code}...", end="")

        try:
            response = safe_request(test_url)
            if response and response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')

                # Chercher s'il y a des cartes (pas de message "0 buah")
                result_text = soup.get_text()
                if "0 buah" not in result_text and "buah" in result_text:
                    available_sets.append(set_code)
                    print(" ✅")
                else:
                    print(" ❌")
            else:
                print(" ❌")
        except:
            print(" ❌")

        # Petit délai pour éviter de surcharger le serveur
        time.sleep(0.5)

    if available_sets:
        print(f"\n✅ {len(available_sets)} sets découverts automatiquement !")
        for i, set_code in enumerate(available_sets):
            print(f"   {i+1}. {set_code}")
        print("🎯 Le scraper va traiter TOUS ces sets dans CET ORDRE !")
        return available_sets
    else:
        print("❌ Aucun set découvert automatiquement")
        print("🔄 Utilisation de la liste de fallback...")
        # Fallback vers les sets connus
        return ['SV10s', 'SV9s', 'SV8s', 'SV8a', 'SVM', 'SV7s', 'SV6s', 'SV5s', 'SV4s', 'SV4a']



def safe_request(url, max_retries=3, delay=2):
    """Effectue une requête HTTP sécurisée avec retry"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=headers, timeout=15)
            response.raise_for_status()
            return response
        except requests.RequestException as e:
            logging.warning(f"Tentative {attempt + 1}/{max_retries} échouée pour {url}: {e}")
            if attempt < max_retries - 1:
                time.sleep(delay * (attempt + 1))
            else:
                logging.error(f"Échec définitif pour {url} après {max_retries} tentatives")
                return None

def get_set_pages(set_code):
    """Détermine le nombre de pages pour un set spécifique"""
    logging.info(f"Détection du nombre de pages pour le set {set_code}...")

    # Tester la première page pour obtenir le total
    test_url = f"https://asia.pokemon-card.com/id/card-search/list/?expansionCodes={set_code}"

    response = safe_request(test_url)
    if not response:
        logging.error(f"Impossible d'accéder au set {set_code}")
        return 0

    soup = BeautifulSoup(response.content, 'html.parser')

    # DEBUG: Chercher tous les textes avec "halaman"
    all_halaman_texts = soup.find_all(string=re.compile(r'halaman'))
    logging.debug(f"Textes avec 'halaman' trouvés: {[t.strip() for t in all_halaman_texts]}")

    # Chercher le texte "/ Total X halaman"
    page_info = soup.find(string=re.compile(r'Total \d+ halaman'))
    if page_info:
        match = re.search(r'Total (\d+) halaman', page_info)
        if match:
            total_pages = int(match.group(1))
            logging.info(f"Set {set_code}: {total_pages} pages trouvées")
            return total_pages
    else:
        logging.debug(f"Aucun texte 'Total X halaman' trouvé pour {set_code}")

    # Fallback: chercher les liens de pagination
    pagination = soup.find_all('a', href=re.compile(r'pageNo=\d+'))
    if pagination:
        max_page = 1
        for link in pagination:
            href = link.get('href', '')
            page_match = re.search(r'pageNo=(\d+)', href)
            if page_match:
                page_num = int(page_match.group(1))
                max_page = max(max_page, page_num)

        logging.info(f"Set {set_code}: {max_page} pages trouvées (via pagination)")
        return max_page

    logging.warning(f"Set {set_code}: Impossible de déterminer le nombre de pages, utilisation de 1")
    return 1

def save_progress(page):
    """Sauvegarde la progression"""
    with open(PROGRESS_FILE, 'w') as f:
        f.write(str(page))
    logging.info(f"Progression sauvegardée: Page {page} traitée.")

def read_progress():
    """Lit la progression sauvegardée"""
    if os.path.exists(PROGRESS_FILE):
        try:
            with open(PROGRESS_FILE, 'r') as f:
                page = int(f.read().strip())
                logging.info(f"Progression trouvée: Dernière page traitée {page}.")
                return page
        except (ValueError, IOError) as e:
            logging.warning(f"Erreur lors de la lecture de la progression: {e}")
    else:
        logging.info(f"{PROGRESS_FILE} non trouvé. Démarrage du scraping depuis le début.")
    return None

def scrape_card_details(card_url, max_retries=3):
    """Scrape les détails d'une carte depuis son URL"""
    
    for attempt in range(max_retries):
        try:
            response = safe_request(card_url)
            if not response:
                continue
                
            soup = BeautifulSoup(response.content, 'html.parser')
            
            card_details = {}
            
            # Extraire le nom indonésien
            h1_tag = soup.find('h1', attrs={'class': 'pageHeader'})
            if h1_tag:
                span_tag = h1_tag.find('span')
                if span_tag:
                    span_tag.decompose()
                card_details['nameCard'] = h1_tag.get_text(strip=True)
            else:
                card_details['nameCard'] = None
            
            # Extraire le numéro de carte
            number_element = soup.find('span', string=re.compile(r'\d+/\d+'))
            if number_element:
                card_details['numberCard'] = number_element.get_text(strip=True)
            else:
                card_details['numberCard'] = None
            
            # Extraire l'URL de l'image (chercher l'image de la carte)
            # L'image de la carte a une URL qui contient '/card-img/' et l'ID de la carte
            img_tag = soup.find('img', src=re.compile(r'/card-img/id\d+\.png'))
            if img_tag and img_tag.get('src'):
                card_details['imageUrl'] = urljoin(BASE_URL, img_tag['src'])
            else:
                # Fallback: chercher toute image avec /card-img/ dans l'URL
                img_tag = soup.find('img', src=re.compile(r'/card-img/'))
                if img_tag and img_tag.get('src'):
                    card_details['imageUrl'] = urljoin(BASE_URL, img_tag['src'])
                else:
                    card_details['imageUrl'] = None
            
            # Extraire l'ID web depuis l'URL
            url_parts = card_url.rstrip('/').split('/')
            if len(url_parts) >= 2 and url_parts[-1].isdigit():
                card_details['webId'] = int(url_parts[-1])
            else:
                card_details['webId'] = None
            
            return card_details
            
        except Exception as e:
            logging.warning(f"Tentative {attempt + 1}/{max_retries} échouée pour {card_url}: {e}")
            if attempt < max_retries - 1:
                time.sleep(2)
    
    logging.error(f"Échec définitif du scraping pour {card_url}")
    return None

def download_image(image_url, local_path, max_retries=3):
    """Télécharge une image depuis une URL"""
    
    if os.path.exists(local_path):
        logging.info(f"L'image existe déjà: {local_path}")
        return True
    
    # Créer le dossier si nécessaire
    os.makedirs(os.path.dirname(local_path), exist_ok=True)
    
    for attempt in range(max_retries):
        try:
            response = safe_request(image_url)
            if response:
                with open(local_path, 'wb') as f:
                    f.write(response.content)
                logging.info(f"Image téléchargée: {local_path}")
                return True
        except Exception as e:
            logging.warning(f"Tentative {attempt + 1}/{max_retries} échouée pour {image_url}: {e}")
            if attempt < max_retries - 1:
                time.sleep(2)
    
    logging.error(f"Échec du téléchargement: {image_url}")
    return False

def check_card_exists(connection, web_id, card_number=None, set_code=None):
    """Vérifie si une carte existe déjà dans la base"""
    cursor = connection.cursor()

    # PRIORITÉ 1: Chercher par web_id (méthode normale)
    web_id_str = str(web_id) if web_id is not None else None
    cursor.execute("SELECT id, name_id, name_en, card_number, image_url, image_url_id FROM pokemon_cards WHERE web_id = %s", (web_id_str,))
    result = cursor.fetchone()

    # PRIORITÉ 2: Si pas trouvé et on a card_number + set_code, chercher par correspondance
    if not result and card_number and set_code:
        # Chercher dans le même set par numéro de carte
        cursor.execute("""
            SELECT c.id, c.name_id, c.name_en, c.card_number, c.image_url, c.image_url_id
            FROM pokemon_cards c
            JOIN pokemon_expansions e ON c.expansion_id = e.id
            WHERE c.card_number = %s AND e.code = %s
        """, (card_number, set_code))
        result = cursor.fetchone()
        if result:
            print(f"   🔄 Correspondance trouvée par numéro de carte {card_number} dans le set {set_code}")

    cursor.close()
    
    if result:
        return {
            'id': result[0],
            'name_id': result[1],
            'name_en': result[2],
            'card_number': result[3],
            'image_url': result[4],
            'image_url_id': result[5]
        }
    return None

def create_card_post(connection, card_data, current_set_code, force_update=False):
    """Crée ou met à jour une carte dans la base de données"""

    if not card_data.get('webId'):
        logging.warning("Aucun webId trouvé pour cette carte")
        return None

    web_id = card_data['webId']

    print(f"\\n🔄 Vérification de la carte existante:")
    set_code = extract_set_code_from_url_or_card(card_data, current_set_code)
    existing_card = check_card_exists(connection, web_id, card_data['numberCard'], set_code)
    
    if existing_card:
        print(f"   Nom (ID): {existing_card['name_id']}")
        print(f"   Nom (EN): {existing_card['name_en']}")
        print(f"   ID: {web_id}")

        # Préparer les nouvelles valeurs
        new_values = {
            'name_id': card_data['nameCard'],
            'name_en': card_data.get('nameCardEn'),
            'card_number': card_data['numberCard'],
            'image_url_id': None,  # Image indonésienne - sera mis à jour après le téléchargement
            'set_code_image_url': None  # Sera mis à jour après le téléchargement
        }
        
        print(f"   📝 Nom indonésien extrait: '{card_data['nameCard']}'")
        print(f"   📝 Nom anglais: '{card_data.get('nameCardEn', 'N/A')}'")
        print(f"   📝 Numéro carte: '{card_data['numberCard']}'")
        
        # Vérifier s'il y a des changements
        current_values = {
            'name_id': existing_card['name_id'],
            'name_en': existing_card['name_en'],
            'card_number': existing_card['card_number'],
            'image_url_id': existing_card['image_url_id'],
            'set_code_image_url': None  # Pas stocké dans existing_card
        }
        
        changed_fields = []
        update_values = []
        update_query_parts = []
        
        for field, new_value in new_values.items():
            current_value = current_values[field]
            
            # Condition améliorée pour gérer les valeurs NULL/vides
            should_update = False
            
            if new_value is not None and new_value != '':
                # Si la nouvelle valeur est valide
                if current_value is None or current_value == '' or current_value != new_value:
                    should_update = True
                # TOUJOURS mettre à jour les noms indonésiens vides (correction du bug principal)
                elif field == 'name_id' and (current_value is None or current_value == ''):
                    should_update = True
                    print(f"   🎯 FORÇAGE mise à jour nom indonésien vide: '{current_value}' -> '{new_value}'")
            
            if should_update:
                changed_fields.append(field)
                update_values.append(new_value)
                update_query_parts.append(f"{field} = %s")
                print(f"   🔄 {field}: '{current_value}' -> '{new_value}'")
        
        # Télécharger l'image indonésienne (TOUJOURS, même sans autres changements)
        if card_data.get('imageUrl'):
            # Utiliser le code du set en cours
            # set_code est déjà défini plus haut avec le bon set
            if set_code:
                image_filename = generate_image_filename(card_data, set_code)
                local_image_path = f"images/{set_code}_ID/{image_filename}"

                # Vérifier si l'image indonésienne existe déjà dans la base
                current_image_id = existing_card.get('image_url_id')
                should_download_image = (
                    current_image_id is None or
                    current_image_id == '' or
                    not os.path.exists(local_image_path)
                )

                if should_download_image:
                    print(f"   📥 Téléchargement de l'image indonésienne...")
                    if download_image(card_data['imageUrl'], local_image_path):
                        print(f"   ✅ Image indonésienne téléchargée: {local_image_path}")
                        # FORCER l'ajout de l'image même sans autres changements
                        if 'image_url_id' not in changed_fields:
                            changed_fields.append('image_url_id')
                            update_values.append(local_image_path)
                            update_query_parts.append("image_url_id = %s")
                            print(f"   🔄 FORCÉ image_url_id: '{current_image_id}' -> '{local_image_path}'")

                        # Image du set (même image pour l'instant)
                        if 'set_code_image_url' not in changed_fields:
                            changed_fields.append('set_code_image_url')
                            update_values.append(local_image_path)
                            update_query_parts.append("set_code_image_url = %s")
                    else:
                        print(f"   ❌ Échec du téléchargement de l'image indonésienne")
                else:
                    print(f"   ℹ️ Image indonésienne déjà présente: {current_image_id}")
        
        # Effectuer la mise à jour si nécessaire
        if changed_fields:
            cursor = connection.cursor()
            update_query = f"UPDATE pokemon_cards SET {', '.join(update_query_parts)} WHERE id = %s"
            update_values.append(existing_card['id'])
            
            cursor.execute(update_query, update_values)
            connection.commit()
            cursor.close()
            
            print(f"   ✅ Carte mise à jour avec {len(changed_fields)} champ(s)")
        else:
            print(f"   ℹ️ Aucune mise à jour nécessaire - les données sont identiques")
        
        return existing_card['id']
    
    else:
        # Carte non trouvée - créons-la automatiquement !
        print(f"   ➕ Carte avec web_id {web_id} non trouvée - création automatique...")
        return create_new_card(connection, card_data, set_code)

def extract_set_code_from_url_or_card(card_data, current_set_code=None):
    """Extrait le code du set depuis les données de la carte avec suffixe indonésien _I"""

    # Si on a le set en cours, l'utiliser directement avec suffixe _I
    if current_set_code:
        return f"{current_set_code.upper()}_I"

    # Fallback : essayer d'extraire depuis l'URL si disponible
    if 'url' in card_data:
        url = card_data['url']
        # Extraire le set code depuis l'URL si possible
        # Format: https://asia.pokemon-card.com/id/card-search/detail/15861/
        pass

    # Mapping des IDs indonésiens vers les codes de sets (basé sur les ranges observés)
    web_id = card_data.get('webId', 0)

    # Ranges d'IDs pour les sets indonésiens avec suffixe _I (FALLBACK SEULEMENT)
    if web_id >= 15861:  # SV10s commence à 15861
        return 'SV10S_I'
    elif web_id >= 15000:  # SV9s
        return 'SV9S_I'
    elif web_id >= 14000:  # SV8a ou autres
        return 'SV8A_I'
    elif web_id >= 13000:  # SVM
        return 'SVM_I'
    elif web_id >= 12000:  # SV8s
        return 'SV8S_I'
    elif web_id >= 11000:  # SV7s
        return 'SV7S_I'
    elif web_id >= 10000:  # SV6s
        return 'SV6S_I'
    elif web_id >= 9900:   # SV4a (Harta Berkilau)
        return 'SV4A_I'
    else:
        # Pour les IDs plus anciens, utiliser un format générique avec suffixe _I
        return f'SET_{web_id // 1000}K_I'

def generate_image_filename(card_data, set_code):
    """Génère le nom de fichier pour l'image"""
    card_number = card_data.get('numberCard', '000')
    web_id = card_data.get('webId', '0')
    
    # Nettoyer le numéro de carte
    clean_number = card_number.split('/')[0].zfill(3) if '/' in card_number else str(web_id).zfill(3)
    
    return f"{clean_number}__{set_code}_ID.jpg"

def parse_arguments():
    """Parse les arguments de ligne de commande"""
    parser = argparse.ArgumentParser(description='Scraper pour les noms indonésiens des cartes Pokemon TCG')
    parser.add_argument('--max-sets', type=int, default=None,
                      help='Nombre maximum de sets à traiter (pour les tests)')
    parser.add_argument('--min-delay', type=float, default=1.0,
                      help='Délai minimum entre les requêtes (secondes)')
    parser.add_argument('--max-delay', type=float, default=3.0,
                      help='Délai maximum entre les requêtes (secondes)')
    parser.add_argument('--max-retries', type=int, default=3,
                      help='Nombre maximum de tentatives par requête')
    parser.add_argument('--reset-progress', action='store_true',
                      help='Réinitialise la progression du scraping et recommence depuis le début.')
    parser.add_argument('--force-update', action='store_true',
                      help='Force la mise à jour des cartes même si elles existent déjà.')
    return parser.parse_args()

def create_new_card(connection, card_data, set_code):
    """Crée une nouvelle carte dans la base de données"""
    cursor = connection.cursor()

    try:
        # 1. Vérifier/créer l'expansion
        expansion_id = get_or_create_expansion(cursor, set_code, card_data)

        # 2. Créer la carte avec nouvelle structure multilingue
        web_id = card_data['webId']
        card_number = card_data['numberCard']
        name_id = card_data['nameCard']

        print(f"   📝 Création nouvelle carte: {name_id} ({card_number})")

        # Récupérer l'ID de la langue indonésienne
        indonesian_language_id = get_language_id(connection, 'id')
        if not indonesian_language_id:
            print("❌ Erreur: Langue indonésienne non trouvée")
            return None

        # Créer ou récupérer l'expansion avec nouvelle structure
        expansion_id_new = get_or_create_expansion_new(connection, set_code)

        # Créer ou récupérer la carte maître
        card_id_master = get_or_create_card_new(connection, expansion_id_new, card_number)

        # Vérifier si la version indonésienne existe déjà
        if card_version_exists_new(connection, card_id_master, indonesian_language_id, web_id):
            print(f"   ⚠️  Version indonésienne de la carte {card_number} existe déjà")
            return None

        # Créer la version indonésienne de la carte
        cursor.execute("""
            INSERT INTO card_versions (
                card_id, language_id, name, web_id, image_url
            ) VALUES (%s, %s, %s, %s, %s)
        """, (card_id_master, indonesian_language_id, name_id, web_id, card_data.get('imageUrl', '')))

        card_version_id = cursor.lastrowid
        connection.commit()

        print(f"   ✅ Version indonésienne créée avec ID: {card_version_id}")

        # 3. Télécharger l'image si disponible (optionnel pour compatibilité)
        if card_data.get('imageUrl'):
            # Construire le chemin local pour l'image
            local_image_path = f"images/{set_code}_ID/{card_number}__{set_code}_ID.jpg"
            success = download_image(card_data['imageUrl'], local_image_path)
            if success:
                cursor.execute("""
                    UPDATE card_versions
                    SET image_url = %s
                    WHERE id = %s
                """, (local_image_path, card_version_id))
                connection.commit()
                print(f"   📸 Image téléchargée: {local_image_path}")

        return card_version_id

    except Exception as e:
        print(f"   ❌ Erreur création carte: {e}")
        connection.rollback()
        return None
    finally:
        cursor.close()

def get_language_id(connection, language_code):
    """Récupère l'ID de la langue"""
    cursor = connection.cursor(dictionary=True)
    cursor.execute("SELECT id FROM languages WHERE code = %s", (language_code,))
    result = cursor.fetchone()
    return result['id'] if result else None

def get_or_create_expansion_new(connection, set_code, set_name_en=None):
    """Récupère ou crée une expansion dans la nouvelle table expansions"""
    cursor = connection.cursor(dictionary=True)

    # Vérifier si l'expansion existe déjà
    cursor.execute("SELECT id FROM expansions WHERE code = %s", (set_code,))
    existing_expansion = cursor.fetchone()

    if existing_expansion:
        print(f"   ✅ Set {set_code} existe déjà avec ID: {existing_expansion['id']}")
        return existing_expansion['id']

    print(f"   📦 Création nouveau set: {set_code}")

    # Nom par défaut basé sur le code
    if not set_name_en:
        set_name_en = f"Set {set_code}"

    cursor.execute("""
        INSERT INTO expansions (code, name_en, region)
        VALUES (%s, %s, 'Indonesia')
    """, (set_code, set_name_en))

    connection.commit()
    expansion_id = cursor.lastrowid
    print(f"   ✅ Set créé avec ID: {expansion_id}")

    return expansion_id

def get_or_create_card_new(connection, expansion_id, card_number, supertype='Pokémon'):
    """Récupère ou crée une carte maître"""
    cursor = connection.cursor(dictionary=True)

    # Chercher une carte existante avec ce numéro dans cette expansion
    cursor.execute("""
        SELECT id FROM cards
        WHERE canonical_number = %s AND expansion_id = %s
    """, (card_number, expansion_id))

    existing_card = cursor.fetchone()
    if existing_card:
        return existing_card['id']

    # Créer une nouvelle carte maître
    cursor.execute("""
        INSERT INTO cards (canonical_number, expansion_id, supertype)
        VALUES (%s, %s, %s)
    """, (card_number, expansion_id, supertype))

    connection.commit()
    return cursor.lastrowid

def card_version_exists_new(connection, card_id, language_id, web_id=None):
    """Vérifie si une version de carte existe déjà pour cette langue"""
    cursor = connection.cursor()
    if web_id:
        cursor.execute("""
            SELECT id FROM card_versions
            WHERE card_id = %s AND language_id = %s AND web_id = %s
        """, (card_id, language_id, web_id))
    else:
        cursor.execute("""
            SELECT id FROM card_versions
            WHERE card_id = %s AND language_id = %s
        """, (card_id, language_id))
    return cursor.fetchone() is not None

def get_or_create_expansion(cursor, set_code, card_data):
    """Récupère ou crée une expansion (ancienne fonction pour compatibilité)"""
    # Chercher l'expansion existante
    cursor.execute("SELECT id FROM pokemon_expansions WHERE code = %s", (set_code,))
    result = cursor.fetchone()

    if result:
        return result[0]

    # Créer une nouvelle expansion
    print(f"   📦 Création nouveau set: {set_code}")

    # Nom par défaut basé sur le code
    set_name = f"Set {set_code}"

    cursor.execute("""
        INSERT INTO pokemon_expansions (
            code, name, created_at, updated_at
        ) VALUES (%s, %s, NOW(), NOW())
    """, (set_code, set_name))

    expansion_id = cursor.lastrowid
    print(f"   ✅ Set créé avec ID: {expansion_id}")

    return expansion_id

def main():
    """Fonction principale"""
    print("🇮🇩 SCRAPER INDONÉSIEN POKEMON TCG")
    print("=" * 50)

    args = parse_arguments()

    if args.reset_progress and os.path.exists(PROGRESS_FILE):
        os.remove(PROGRESS_FILE)
        logging.info(f"{PROGRESS_FILE} supprimé. Le scraping recommencera depuis le début.")

    # Configuration de la base de données
    print("\n=== Configuration de la base de données ===")
    config = get_db_config()
    print(f"Host: {config['host']}")
    print(f"Database: {config['database']}")
    print(f"Port: {config['port']}")
    print(f"User: {config['user']}")

    try:
        connection = mysql.connector.connect(**config)
        print("✅ Connexion à la base de données réussie !")
    except mysql.connector.Error as e:
        print(f"❌ Erreur de connexion à la base de données: {e}")
        exit(1)

    try:
        # Vérifier que la colonne name_id existe
        cursor = connection.cursor()
        cursor.execute("SHOW COLUMNS FROM pokemon_cards LIKE 'name_id'")
        if not cursor.fetchone():
            print("❌ Colonne name_id manquante. Ajout en cours...")
            cursor.execute("ALTER TABLE pokemon_cards ADD COLUMN name_id VARCHAR(255) DEFAULT NULL")
            connection.commit()
            print("✅ Colonne name_id ajoutée")
        else:
            print("✅ Colonne name_id existe")
        cursor.close()

        print("\nTraitement des sets indonésiens...")

        # DÉCOUVERTE AUTOMATIQUE DE TOUS LES SETS DANS L'ORDRE DU SITE !
        indonesian_sets = discover_all_indonesian_sets()
        if not indonesian_sets:
            print("❌ Aucun set découvert ! Arrêt du scraper.")
            return

        # Traiter chaque set indonésien dans l'ordre du site
        sets_processed = 0

        for set_code in indonesian_sets:
            if args.max_sets and sets_processed >= args.max_sets:
                logging.info(f"Limite de {args.max_sets} set(s) atteinte. Arrêt du scraping.")
                break

            logging.info(f"🇮🇩 Traitement du set {set_code}...")

            # Déterminer le nombre de pages pour ce set
            total_pages = get_set_pages(set_code)
            if total_pages == 0:
                logging.warning(f"Set {set_code} ignoré (aucune page trouvée)")
                continue

            # Traiter toutes les pages de ce set
            for page_num in range(1, total_pages + 1):
                logging.info(f"Traitement du set {set_code}, page {page_num}/{total_pages}...")

                if page_num == 1:
                    url = f"https://asia.pokemon-card.com/id/card-search/list/?expansionCodes={set_code}"  # Première page sans pageNo
                else:
                    url = f"https://asia.pokemon-card.com/id/card-search/list/?pageNo={page_num}&expansionCodes={set_code}"

                response = safe_request(url)
                if not response:
                    logging.error(f"Erreur lors de la récupération de {set_code} page {page_num}")
                    continue

                soup = BeautifulSoup(response.content, 'html.parser')

                # Trouver tous les liens vers les cartes
                card_links = soup.find_all('a', href=re.compile(r'/id/card-search/detail/\d+/'))

                if not card_links:
                    logging.warning(f"Aucune carte trouvée sur {set_code} page {page_num}")
                    continue

                logging.info(f"Trouvé {len(card_links)} cartes sur {set_code} page {page_num}")

                for card_idx, card_link in enumerate(card_links):
                    card_url = urljoin(BASE_URL, card_link['href'])
                    logging.info(f"Traitement carte {card_idx + 1}/{len(card_links)}: {card_url}")

                    card_details = scrape_card_details(card_url, max_retries=args.max_retries)
                    if card_details:
                        card_db_id = create_card_post(connection, card_details, set_code, force_update=args.force_update)
                        if card_db_id:
                            logging.info(f"Carte ajoutée/mise à jour avec succès (ID DB: {card_db_id}, Web ID: {card_details.get('webId')})")
                        else:
                            logging.warning(f"Échec de l'ajout/mise à jour de la carte: {card_url}")
                    else:
                        logging.error(f"Échec du scraping des détails pour: {card_url}")

                    time.sleep(random.uniform(args.min_delay / 2, args.max_delay / 2))

                time.sleep(random.uniform(args.min_delay, args.max_delay))

            sets_processed += 1
            logging.info(f"Set {set_code} terminé ({sets_processed}/{len(indonesian_sets)})")

        logging.info("Scraping indonésien terminé avec succès!")

        # Le reste du code de la boucle n'est plus nécessaire
        return



    except Exception as e:
        logging.error(f"Erreur durant le scraping: {e}")
        raise
    finally:
        if connection:
            connection.close()
            print("Connexion à la base de données fermée")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Scraping interrompu par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur fatale: {e}")
        raise
