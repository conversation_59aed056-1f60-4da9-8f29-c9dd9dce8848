from flask import Flask, render_template, request, url_for, redirect, send_from_directory, jsonify
import mysql.connector
import os
import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
import json
from config.database import get_db_config

app = Flask(__name__)

def get_multilang_set_name(set_code, set_name_thai, lang):
    """
    Fonction simplifiée pour obtenir le nom d'un set dans différentes langues
    """
    # Pour l'instant, retourne le nom thaï ou le code selon la langue
    if lang == 'thai' and set_name_thai:
        return set_name_thai
    elif lang == 'english':
        # Retourne le code pour l'anglais si pas de nom spécifique
        return set_code
    else:
        # Pour les autres langues, retourne le code
        return set_code

# Servir le dossier images/ à la racine du projet
@app.route('/images/<path:filename>')
def images(filename):
    return send_from_directory(os.path.join(os.path.dirname(__file__), '../images'), filename)

# Configuration de la base de données
wp_config = get_db_config()

def find_related_sets(set_code, cursor):
    """
    Trouve tous les sets liés (avec et sans suffixes _I, _T) pour un code donné.

    Args:
        set_code: Le code du set à chercher (ex: 'SV9s', 'SV9s_I', 'SV9s_T')
        cursor: Curseur de base de données

    Returns:
        Liste des codes de sets liés, ou liste vide si aucun trouvé
    """
    # Nettoyer le code d'entrée pour obtenir le code de base
    base_code = set_code
    if base_code.endswith('_I') or base_code.endswith('_T'):
        base_code = base_code[:-2]

    # Chercher tous les sets avec ce code de base (avec et sans suffixes)
    possible_codes = [base_code, f"{base_code}_I", f"{base_code}_T"]

    related_sets = []
    for code in possible_codes:
        cursor.execute("SELECT code FROM pokemon_expansions WHERE code = %s", (code,))
        result = cursor.fetchone()
        if result:
            related_sets.append(code)

    return related_sets

def get_languages():
    """Get available languages based on multilingual columns with data"""
    connection = mysql.connector.connect(**wp_config)
    cursor = connection.cursor()

    # Check which language columns have data
    languages = []
    language_columns = ['name_en', 'name_th', 'name_ja', 'name_ko', 'name_zh', 'name_fr', 'name_de', 'name_it', 'name_es', 'name_id']

    for col in language_columns:
        cursor.execute(f"SELECT COUNT(*) FROM pokemon_expansions WHERE {col} IS NOT NULL AND {col} != ''")
        count = cursor.fetchone()[0]
        if count > 0:
            # Map column names to language names
            lang_map = {
                'name_en': 'english',
                'name_th': 'thai',
                'name_ja': 'japanese',
                'name_ko': 'korean',
                'name_zh': 'chinese',
                'name_fr': 'french',
                'name_de': 'german',
                'name_it': 'italian',
                'name_es': 'spanish',
                'name_id': 'indonesian'
            }
            languages.append(lang_map[col])

    cursor.close()
    connection.close()
    return sorted(languages)

@app.route('/')
def index():
    # Dashboard with overview statistics
    connection = mysql.connector.connect(**wp_config)
    cursor = connection.cursor()

    # Get statistics
    cursor.execute("SELECT COUNT(*) FROM pokemon_cards")
    total_cards = cursor.fetchone()[0]

    cursor.execute("SELECT COUNT(*) FROM pokemon_expansions")
    total_sets = cursor.fetchone()[0]

    # Count languages based on available multilingual data
    language_columns = ['name_en', 'name_th', 'name_ja', 'name_ko', 'name_zh', 'name_fr', 'name_de', 'name_it', 'name_es', 'name_id']
    total_languages = 0
    for col in language_columns:
        cursor.execute(f"SELECT COUNT(*) FROM pokemon_expansions WHERE {col} IS NOT NULL AND {col} != ''")
        if cursor.fetchone()[0] > 0:
            total_languages += 1

    cursor.execute("SELECT COUNT(*) FROM pokemon_cards WHERE name_en IS NOT NULL AND name_en != ''")
    cards_with_english = cursor.fetchone()[0]

    # Get recent sets (last 10)
    cursor.execute("SELECT code, name, name_en FROM pokemon_expansions ORDER BY id DESC LIMIT 10")
    recent_sets_raw = cursor.fetchall()

    # Add language info based on available data
    recent_sets = []
    for code, name, name_en in recent_sets_raw:
        if name_en:
            language = 'english'
        else:
            language = 'thai'
        recent_sets.append((code, name, language))

    cursor.close()
    connection.close()

    stats = {
        'total_cards': total_cards,
        'total_sets': total_sets,
        'total_languages': total_languages,
        'cards_with_english': cards_with_english,
        'english_coverage': round((cards_with_english / total_cards * 100), 1) if total_cards > 0 else 0,
        'recent_sets': recent_sets
    }

    return render_template('dashboard.html', stats=stats)

@app.route('/sets')
def sets():
    connection = mysql.connector.connect(**wp_config)
    cursor = connection.cursor(dictionary=True)

    # Afficher SEULEMENT les sets qui ont des cartes
    query = """
    SELECT DISTINCT e.*, COUNT(c.id) as card_count
    FROM pokemon_expansions e
    LEFT JOIN pokemon_cards c ON e.id = c.expansion_id
    GROUP BY e.id
    HAVING card_count > 0
    ORDER BY e.code
    """
    cursor.execute(query)
    sets = cursor.fetchall()



    cursor.close()
    connection.close()

    # Add multilingual names for display
    display_langs = ['english', 'japanese', 'korean', 'chinese', 'thai', 'indonesian', 'french', 'german', 'italian', 'spanish']
    for s in sets:
        s['multilang_names'] = {}
        for lang in display_langs:
            s['multilang_names'][lang] = get_multilang_set_name(set_code=s['code'], set_name_thai=s['name'], lang=lang)

    return render_template('sets.html', sets=sets, display_langs=display_langs)

@app.route('/sets/<set_code>')
def show_cards(set_code):
    sort = request.args.get('sort', 'card_number')
    order = request.args.get('order', 'asc')

    connection = mysql.connector.connect(**wp_config)
    cursor = connection.cursor(dictionary=True)

    # FUSION UNIVERSELLE : Chercher tous les sets liés
    related_sets = find_related_sets(set_code, cursor)

    if not related_sets:
        cursor.close()
        connection.close()
        return render_template('error.html',
                             error_title="Set non trouvé",
                             error_message=f"Le set '{set_code}' n'existe pas dans la base de données."), 404

    print(f"🔄 FUSION UNIVERSELLE: {set_code} -> {related_sets}")

    # Prendre le premier set comme référence pour les métadonnées
    cursor.execute("SELECT * FROM pokemon_expansions WHERE code = %s", (related_sets[0],))
    set_info = cursor.fetchone()

    # Récupérer les cartes du set
    if sort == 'name':
        order_clause = f"name_en {order.upper()}"
    elif sort == 'rarity':
        order_clause = f"rarity {order.upper()}"
    else:  # card_number par défaut
        order_clause = f"""
        CASE
            WHEN SUBSTRING_INDEX(card_number, '/', 1) REGEXP '^[0-9]+$'
            THEN CAST(SUBSTRING_INDEX(card_number, '/', 1) AS UNSIGNED)
            ELSE 9999
        END {order.upper()},
        CASE
            WHEN SUBSTRING_INDEX(card_number, '/', -1) REGEXP '^[0-9]+$'
            THEN CAST(SUBSTRING_INDEX(card_number, '/', -1) AS UNSIGNED)
            ELSE 9999
        END {order.upper()},
        card_number {order.upper()}
        """

    # FILTRAGE INTELLIGENT : Détecter le pattern du set pour éviter le mélange
    # D'abord, récupérer TOUTES les cartes de TOUS les sets liés pour détecter le pattern
    expansion_ids = []
    for related_code in related_sets:
        cursor.execute("SELECT id FROM pokemon_expansions WHERE code = %s", (related_code,))
        exp = cursor.fetchone()
        if exp:
            expansion_ids.append(exp['id'])

    if expansion_ids:
        placeholders = ','.join(['%s'] * len(expansion_ids))
        cursor.execute(f"SELECT card_number FROM pokemon_cards WHERE expansion_id IN ({placeholders})", expansion_ids)
        all_cards = cursor.fetchall()
    else:
        all_cards = []

    # Détecter le pattern majoritaire (ex: /166, /182, /187)
    patterns = {}
    for card in all_cards:
        if '/' in card['card_number']:
            total = card['card_number'].split('/')[-1]
            patterns[total] = patterns.get(total, 0) + 1

    print(f"🔍 ANALYSE {set_code}: {len(all_cards)} cartes, patterns trouvés: {patterns}")

    # Prendre le pattern le plus fréquent
    if patterns:
        main_pattern = max(patterns, key=patterns.get)
        print(f"🎯 Pattern détecté pour {set_code}: /{main_pattern} ({patterns[main_pattern]} cartes)")

        query = f"""
        SELECT * FROM pokemon_cards
        WHERE expansion_id IN ({placeholders}) AND card_number LIKE %s
        ORDER BY {order_clause}
        """
        cursor.execute(query, expansion_ids + [f"%/{main_pattern}"])
    else:
        # Fallback si pas de pattern détecté
        print(f"⚠️  AUCUN PATTERN pour {set_code} - affichage de toutes les cartes")
        query = f"""
        SELECT * FROM pokemon_cards
        WHERE expansion_id IN ({placeholders})
        ORDER BY {order_clause}
        """
        cursor.execute(query, expansion_ids)

    raw_cards = cursor.fetchall()

    # FUSION INTELLIGENTE : Fusionner les cartes ayant le même card_number
    cards_dict = {}
    for card in raw_cards:
        card_num = card['card_number']

        if card_num not in cards_dict:
            # Première occurrence de cette carte
            cards_dict[card_num] = dict(card)
        else:
            # Fusionner avec la carte existante
            existing_card = cards_dict[card_num]

            # Fusionner tous les champs de langue et d'image
            language_fields = ['name_en', 'name_th', 'name_id', 'name_ja', 'name_ko', 'name_zh',
                             'name_fr', 'name_de', 'name_it', 'name_es',
                             'image_url_en', 'image_url_th', 'image_url_id', 'image_url_ja',
                             'image_url_ko', 'image_url_zh', 'image_url_fr', 'image_url_de',
                             'image_url_it', 'image_url_es']

            for field in language_fields:
                if field in card and card[field] and not existing_card.get(field):
                    existing_card[field] = card[field]

            # Prendre les autres champs de la carte qui a le plus de données
            other_fields = ['rarity', 'type', 'hp', 'artist', 'description']
            for field in other_fields:
                if field in card and card[field] and not existing_card.get(field):
                    existing_card[field] = card[field]

    # Convertir le dictionnaire en liste et trier
    cards = list(cards_dict.values())

    # Re-trier selon les critères demandés
    if sort == 'name':
        cards.sort(key=lambda x: x.get('name_en', ''), reverse=(order == 'desc'))
    elif sort == 'rarity':
        cards.sort(key=lambda x: x.get('rarity', ''), reverse=(order == 'desc'))
    else:  # card_number par défaut
        def sort_card_number(card):
            card_num = card.get('card_number', '0/0')
            if '/' in card_num:
                parts = card_num.split('/')
                try:
                    return (int(parts[0]), int(parts[1]))
                except ValueError:
                    return (0, 0)
            return (0, 0)

        cards.sort(key=sort_card_number, reverse=(order == 'desc'))

    print(f"✅ FUSION TERMINÉE: {len(raw_cards)} cartes brutes -> {len(cards)} cartes fusionnées")

    cursor.close()
    connection.close()

    return render_template('cards.html', set_info=set_info, cards=cards, sort=sort, order=order)

@app.route('/card/<int:card_id>')
def card_detail(card_id):
    connection = mysql.connector.connect(**wp_config)
    cursor = connection.cursor(dictionary=True)
    cursor.execute("""
        SELECT c.*
        FROM pokemon_cards c
        WHERE c.id = %s
    """, (card_id,))
    card = cursor.fetchone()
    cursor.close()
    connection.close()
    return render_template('card_detail.html', card=card)

MAPPING_FILE = os.path.join(os.path.dirname(__file__), '../auto_set_code_to_url.json')

def load_mapping():
    if os.path.exists(MAPPING_FILE):
        with open(MAPPING_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {}

def save_mapping(mapping):
    with open(MAPPING_FILE, 'w', encoding='utf-8') as f:
        json.dump(mapping, f, ensure_ascii=False, indent=2)

@app.route('/mapping')
def mapping_editor():
    mapping = load_mapping()
    return render_template('editor.html', mapping=mapping)

@app.route('/bulk-mapping')
def bulk_mapping_editor():
    """Interface pour modifier tous les mappings en masse"""
    # Charger le mapping existant
    mapping = load_mapping()

    # Récupérer tous les sets de la base de données
    connection = mysql.connector.connect(**wp_config)
    cursor = connection.cursor(dictionary=True)
    cursor.execute("""
        SELECT code, name, total_cards,
               (SELECT COUNT(*) FROM pokemon_cards WHERE expansion_id = pokemon_expansions.id) as actual_cards
        FROM pokemon_expansions
        ORDER BY code
    """)
    all_sets = cursor.fetchall()
    cursor.close()
    connection.close()

    # Enrichir avec les URLs existantes
    for set_info in all_sets:
        set_info['url'] = mapping.get(set_info['code'], '')
        set_info['has_cards'] = set_info['actual_cards'] > 0

    return render_template('bulk_editor.html', sets=all_sets, total_sets=len(all_sets))

@app.route('/bulk-update', methods=['POST'])
def bulk_update_mapping():
    """Mettre à jour tous les mappings en masse"""
    try:
        data = request.json
        mappings = data.get('mappings', {})

        # Sauvegarder tous les mappings
        save_mapping(mappings)

        return jsonify({'success': True, 'message': f'{len(mappings)} mappings mis à jour'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/update', methods=['POST'])
def update_mapping():
    mapping = load_mapping()
    data = request.json
    code = data.get('code')
    url = data.get('url')
    if not code:
        return jsonify({'success': False, 'error': 'Code manquant'}), 400
    mapping[code] = url
    save_mapping(mapping)
    return jsonify({'success': True})

@app.route('/add', methods=['POST'])
def add_mapping():
    mapping = load_mapping()
    data = request.json
    code = data.get('code')
    url = data.get('url')
    if not code:
        return jsonify({'success': False, 'error': 'Code manquant'}), 400
    if code in mapping:
        return jsonify({'success': False, 'error': 'Code déjà existant'}), 400
    mapping[code] = url
    save_mapping(mapping)
    return jsonify({'success': True})

@app.route('/delete', methods=['POST'])
def delete_mapping():
    mapping = load_mapping()
    data = request.json
    code = data.get('code')
    if not code or code not in mapping:
        return jsonify({'success': False, 'error': 'Code introuvable'}), 400
    del mapping[code]
    save_mapping(mapping)
    return jsonify({'success': True})

@app.route('/scraping')
def scraping_dashboard():
    """Scraping management dashboard"""
    return render_template('scraping.html')

@app.route('/quality')
def quality_dashboard():
    """Data quality dashboard"""
    connection = mysql.connector.connect(**wp_config)
    cursor = connection.cursor()

    # Quality metrics
    cursor.execute("SELECT COUNT(*) FROM pokemon_cards WHERE name_en IS NULL OR name_en = ''")
    missing_english = cursor.fetchone()[0]

    cursor.execute("SELECT COUNT(*) FROM pokemon_cards WHERE rarity IS NULL OR rarity = ''")
    missing_rarity = cursor.fetchone()[0]

    cursor.execute("SELECT COUNT(*) FROM pokemon_cards WHERE card_number IS NULL OR card_number = ''")
    missing_numbers = cursor.fetchone()[0]

    cursor.execute("SELECT COUNT(*) FROM pokemon_cards")
    total_cards = cursor.fetchone()[0]

    # Language distribution based on available multilingual data
    language_stats = []
    language_columns = [
        ('english', 'name_en'),
        ('thai', 'name_th'),
        ('japanese', 'name_ja'),
        ('korean', 'name_ko'),
        ('chinese', 'name_zh'),
        ('french', 'name_fr'),
        ('german', 'name_de'),
        ('italian', 'name_it'),
        ('spanish', 'name_es'),
        ('indonesian', 'name_id')
    ]

    for lang_name, column in language_columns:
        cursor.execute(f"SELECT COUNT(*) FROM pokemon_expansions WHERE {column} IS NOT NULL AND {column} != ''")
        count = cursor.fetchone()[0]
        if count > 0:
            language_stats.append((lang_name, count))

    cursor.close()
    connection.close()

    quality_stats = {
        'missing_english': missing_english,
        'missing_rarity': missing_rarity,
        'missing_numbers': missing_numbers,
        'total_cards': total_cards,
        'language_stats': language_stats,
        'completeness': round(((total_cards - missing_english - missing_rarity - missing_numbers) / (total_cards * 3) * 100), 1) if total_cards > 0 else 0
    }

    return render_template('quality.html', stats=quality_stats)

@app.route('/search')
def search():
    """Advanced search interface"""
    query = request.args.get('q', '')
    language = request.args.get('language', '')
    set_code = request.args.get('set', '')

    results = []
    if query:
        connection = mysql.connector.connect(**wp_config)
        cursor = connection.cursor(dictionary=True)

        # Build search query using direct columns
        search_query = """
            SELECT c.*,
                pe.code as set_code,
                pe.name as set_name
            FROM pokemon_cards c
            LEFT JOIN pokemon_expansions pe ON c.expansion_id = pe.id
            WHERE (c.name_th LIKE %s OR c.name_en LIKE %s OR c.card_number LIKE %s)
        """
        params = [f'%{query}%', f'%{query}%', f'%{query}%']

        if language:
            # Filter by language using multilingual columns
            lang_column_map = {
                'english': 'name_en',
                'thai': 'name_th',
                'japanese': 'name_ja',
                'korean': 'name_ko',
                'chinese': 'name_zh',
                'french': 'name_fr',
                'german': 'name_de',
                'italian': 'name_it',
                'spanish': 'name_es',
                'indonesian': 'name_id'
            }
            if language in lang_column_map:
                column = lang_column_map[language]
                search_query += f" AND pe.{column} IS NOT NULL AND pe.{column} != ''"

        if set_code:
            search_query += " AND pe.code = %s"
            params.append(set_code)

        search_query += " LIMIT 100"

        cursor.execute(search_query, params)
        results = cursor.fetchall()
        cursor.close()
        connection.close()

    # Get available languages and sets for filters
    languages = get_languages()
    connection = mysql.connector.connect(**wp_config)
    cursor = connection.cursor()
    cursor.execute("SELECT DISTINCT code, name, name_en FROM pokemon_expansions ORDER BY code")
    sets_raw = cursor.fetchall()

    # Format sets with preferred names (English if available, otherwise original)
    sets = []
    for code, name, name_en in sets_raw:
        display_name = name_en if name_en else name
        sets.append((code, display_name))
    cursor.close()
    connection.close()

    return render_template('search.html',
                         results=results,
                         query=query,
                         languages=languages,
                         sets=sets,
                         selected_language=language,
                         selected_set=set_code)

@app.route('/sets/unified')
def sets_unified():
    """Vue unifiée des sets avec relations multilingues"""
    connection = mysql.connector.connect(**wp_config)
    cursor = connection.cursor(dictionary=True)

    # Utiliser la vue unifiée
    query = "SELECT * FROM unified_sets_view ORDER BY code"
    cursor.execute(query)
    sets = cursor.fetchall()

    # Enrichir avec les relations
    for s in sets:
        related_ids = []
        if s['related_set_ids']:
            related_ids.extend(s['related_set_ids'].split(','))
        if s['related_set_ids_reverse']:
            related_ids.extend(s['related_set_ids_reverse'].split(','))

        if related_ids:
            placeholders = ','.join(['%s'] * len(related_ids))
            cursor.execute(f"SELECT id, code, name FROM pokemon_expansions WHERE id IN ({placeholders})", related_ids)
            s['related_sets'] = cursor.fetchall()
        else:
            s['related_sets'] = []

    cursor.close()
    connection.close()

    return render_template('sets_unified.html', sets=sets)

@app.route('/sets/by-language/<language>')
def sets_by_language(language):
    """Vue des sets filtrés par langue"""
    connection = mysql.connector.connect(**wp_config)
    cursor = connection.cursor(dictionary=True)

    # Filtrer par langue
    if language == 'english':
        query = "SELECT * FROM unified_sets_view WHERE primary_language LIKE '%English%' ORDER BY code"
    elif language == 'thai':
        query = "SELECT * FROM unified_sets_view WHERE primary_language LIKE '%Thai%' ORDER BY code"
    elif language == 'indonesian':
        query = "SELECT * FROM unified_sets_view WHERE primary_language = 'Indonesian' ORDER BY code"
    else:
        query = "SELECT * FROM unified_sets_view ORDER BY code"

    cursor.execute(query)
    sets = cursor.fetchall()

    cursor.close()
    connection.close()

    return render_template('sets_by_language.html', sets=sets, language=language)

@app.route('/favicon.ico')
def favicon():
    """Servir le favicon"""
    return send_from_directory(os.path.join(app.root_path, 'static'),
                               'favicon.ico', mimetype='image/vnd.microsoft.icon')

if __name__ == "__main__":
    # Configuration adaptée pour Raspberry Pi
    import os
    debug_mode = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    port = int(os.getenv('FLASK_PORT', '5051'))

    app.run(host="0.0.0.0", port=port, debug=debug_mode, threaded=True)