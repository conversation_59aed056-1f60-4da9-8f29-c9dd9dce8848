#!/usr/bin/env python3
"""
Scraper <PERSON><PERSON><PERSON> - <PERSON><PERSON>mon TCG
Récupère TOUS les sets et TOUTES les cartes depuis le site officiel thaï

RÈGLES NON NÉGOCIABLES :
- Scraping complet de tous les sets disponibles
- Récupération de toutes les cartes de chaque set (pas seulement 20)
- Fonctionnement autonome et indépendant
- Source unique : asia.pokemon-card.com/th/
"""

import requests
from bs4 import BeautifulSoup
import mysql.connector
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.database import get_db_config
import time
import random
import logging
import argparse
import re
from urllib.parse import urljoin

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# URLs du site thaï
BASE_URL = "https://asia.pokemon-card.com"
LIST_URL = "https://asia.pokemon-card.com/th/card-search/list/?expansionCodes="

def extract_real_set_info(soup):
    """Extrait les VRAIES informations du set depuis la page"""

    set_info = {
        'code': None,
        'name_th': None,
        'name_en': None
    }

    try:
        # Chercher le lien vers le set (méthode principale)
        set_links = soup.find_all('a', href=re.compile(r'expansionCodes='))

        for link in set_links:
            href = link.get('href', '')
            text = link.get_text(strip=True)

            # Extraire le code du set depuis l'URL
            code_match = re.search(r'expansionCodes=([^&]+)', href)
            if code_match:
                set_info['code'] = code_match.group(1)
                set_info['name_th'] = text

                # Essayer de deviner le nom anglais
                if 'starter' in text.lower():
                    set_info['name_en'] = f"Starter Deck {set_info['code']}"
                elif 'sv' in set_info['code'].lower():
                    set_info['name_en'] = f"Scarlet & Violet {set_info['code']}"
                else:
                    set_info['name_en'] = f"Set {set_info['code']}"

                break

        # Fallback: chercher dans le texte de la page
        if not set_info['code']:
            page_text = soup.get_text()

            # Patterns de codes de sets
            code_patterns = [
                r'\b(SC\d+[A-Z]*)\b',    # SC1D, SC2, etc.
                r'\b(SV\d+[a-z]*)\b',    # SV1s, SV2a, etc.
                r'\b(S\d+[a-z]*)\b',     # S1, S2a, etc.
            ]

            for pattern in code_patterns:
                matches = re.findall(pattern, page_text, re.IGNORECASE)
                if matches:
                    set_info['code'] = matches[0].upper()
                    set_info['name_en'] = f"Set {set_info['code']}"
                    break

    except Exception as e:
        logger.error(f"Erreur extraction set info: {e}")

    return set_info

def get_card_details(card_id):
    """Récupère les détails d'une carte par son ID avec VRAI set"""

    detail_url = f"https://asia.pokemon-card.com/th/card-search/detail/{card_id}/"

    try:
        response = requests.get(detail_url, timeout=10)
        if response.status_code != 200:
            return None

        soup = BeautifulSoup(response.content, 'html.parser')

        # Extraire le nom thaï
        name_element = soup.find('h1', class_='pageHeader')
        if not name_element:
            return None

        # Nettoyer le nom (supprimer les spans)
        for span in name_element.find_all('span'):
            span.decompose()
        name_th = name_element.get_text(strip=True)

        # FILTRER LES PAGES D'ERREUR
        if "ผลลัพธ์การค้นหาการ์ด" in name_th:
            return None

        # Extraire le numéro de carte
        number_element = soup.find('span', string=re.compile(r'\d+/\d+'))
        card_number = number_element.get_text(strip=True) if number_element else "???"

        if card_number == "???":
            return None

        # Extraire l'URL de l'image
        image_element = soup.find('img', src=re.compile(r'/card-img/'))
        image_url = ''
        if image_element and image_element.get('src'):
            image_url = urljoin(BASE_URL, image_element['src'])

        # NOUVEAU: Extraire les VRAIES informations du set
        set_info = extract_real_set_info(soup)

        return {
            'card_id': card_id,
            'web_id': card_id,
            'name_th': name_th,
            'card_number': card_number,
            'image_url': image_url,
            'set_code': set_info['code'],
            'set_name_th': set_info['name_th'],
            'set_name_en': set_info['name_en']
        }

    except Exception as e:
        logger.error(f"Erreur récupération détails carte {card_id}: {e}")
        return None

def find_latest_card_id():
    """Trouve automatiquement l'ID de la dernière carte disponible"""
    print("🔍 RECHERCHE AUTOMATIQUE DE LA DERNIÈRE CARTE...")

    # Commencer par tester des IDs élevés pour trouver la zone active
    test_ranges = [
        (20000, 25000),  # Zone probable des cartes récentes
        (15000, 20000),  # Zone intermédiaire
        (10000, 15000),  # Zone plus ancienne
        (25000, 30000),  # Au cas où il y aurait encore plus
        (30000, 50000),  # Zone très récente
    ]

    latest_found = 0

    for start, end in test_ranges:
        print(f"   🔍 Test range {start}-{end}...")

        # Tester quelques IDs dans cette range
        test_ids = [start, start + 500, start + 1000, end - 1000, end - 500, end]

        for test_id in test_ids:
            card_details = get_card_details(test_id)
            if card_details:
                latest_found = max(latest_found, test_id)
                print(f"   ✅ Carte trouvée à l'ID {test_id}: {card_details['name_th']}")
                break

    # Affiner la recherche autour de la zone trouvée
    if latest_found > 0:
        print(f"🎯 Affinement autour de l'ID {latest_found}...")

        # Chercher vers le haut pour trouver la vraie limite
        search_id = latest_found
        while search_id < latest_found + 5000:
            if get_card_details(search_id):
                latest_found = search_id
                search_id += 100
            else:
                search_id += 100
                if search_id - latest_found > 1000:  # Gap trop grand
                    break

    final_max = latest_found + 2000 if latest_found > 0 else 25000
    print(f"✅ Limite dynamique déterminée: {final_max}")
    return final_max

def scrape_all_thai_cards_progressive(connection):
    """Scrape TOUTES les cartes avec sauvegarde progressive (évite la perte de données)"""
    print(f"\n🎯 SCRAPING PROGRESSIF DE TOUTES LES CARTES THAÏ")
    print("=" * 60)

    # Déterminer automatiquement la limite supérieure
    max_id = find_latest_card_id()

    # Récupérer l'ID de la langue thaï
    thai_language_id = get_language_id(connection, 'th')
    if not thai_language_id:
        print("❌ Erreur: Langue thaï non trouvée dans la base")
        return 0

    current_id = 1
    consecutive_failures = 0
    max_consecutive_failures = 200  # Tolérance élevée pour les gaps entre sets
    saved_count = 0
    skipped_count = 0

    print(f"🔢 Scanning DYNAMIQUE des IDs de {current_id} à {max_id}")
    print("💾 SAUVEGARDE PROGRESSIVE - Chaque carte sauvegardée immédiatement")
    print("🎯 Récupération de TOUTES les cartes disponibles (y compris les nouveaux sets)...")

    while current_id <= max_id and consecutive_failures < max_consecutive_failures:
        try:
            if current_id % 100 == 0:
                print(f"📊 Progrès: {current_id}/{max_id} IDs testés, {saved_count} cartes sauvegardées, {skipped_count} ignorées")

            # Tester si cette carte existe
            card_details = get_card_details(current_id)

            if card_details:
                consecutive_failures = 0
                print(f"   ✅ ID {current_id}: {card_details['name_th']} ({card_details['card_number']})")

                # SAUVEGARDE IMMÉDIATE - SCRAPING COMPLET FORCÉ
                try:
                    # SCRAPING COMPLET : On scrappe toutes les cartes sans vérifier l'existence
                    print(f"      🔄 Scraping complet forcé...")

                    # Vérifier si la carte existe déjà pour éviter les doublons
                    if not card_exists_in_db(connection, card_details['web_id'], thai_language_id):
                        # Récupérer ou créer l'expansion
                        if card_details.get('set_code'):
                            expansion_id = get_or_create_expansion(
                                connection,
                                card_details['set_code'],
                                card_details.get('set_name_en')
                            )
                        else:
                            # Fallback si pas de set trouvé
                            expansion_id = get_or_create_expansion(connection, 'UNKNOWN_T', 'Unknown Set')

                        # Récupérer ou créer la carte maître
                        card_id = get_or_create_card(
                            connection,
                            expansion_id,
                            card_details['card_number']
                        )

                        # Créer la version thaï de la carte
                        cursor = connection.cursor()
                        cursor.execute("""
                            INSERT INTO card_versions (
                                card_id, language_id, name, image_url, web_id
                            ) VALUES (%s, %s, %s, %s, %s)
                        """, (
                            card_id,
                            thai_language_id,
                            card_details['name_th'],
                            card_details['image_url'],
                            card_details['web_id']
                        ))

                        connection.commit()  # Commit immédiat
                        saved_count += 1
                        print(f"      💾 Sauvegardée immédiatement (ID: {cursor.lastrowid})")
                    else:
                        skipped_count += 1
                        print(f"      ⚠️  Carte déjà existante - ignorée")

                except Exception as save_error:
                    print(f"      ❌ Erreur sauvegarde: {save_error}")
                    connection.rollback()

            else:
                consecutive_failures += 1
                if consecutive_failures <= 5:  # Afficher seulement les premiers échecs
                    print(f"   ❌ ID {current_id}: non trouvé (échec {consecutive_failures})")

            current_id += 1

            # Pause pour ne pas surcharger le serveur
            time.sleep(random.uniform(0.2, 0.5))

        except Exception as e:
            consecutive_failures += 1
            logger.error(f"Erreur ID {current_id}: {e}")
            current_id += 1
            continue

    print(f"\n📊 RÉSULTAT FINAL:")
    print(f"   • IDs testés: {current_id - 1}")
    print(f"   • Cartes sauvegardées: {saved_count}")
    print(f"   • Cartes ignorées (déjà existantes): {skipped_count}")
    print(f"   • Échecs consécutifs: {consecutive_failures}")

    return saved_count

def get_or_create_expansion(connection, set_code, set_name_en=None):
    """Récupère ou crée une expansion dans la nouvelle table expansions"""

    cursor = connection.cursor(dictionary=True)

    # Vérifier si l'expansion existe déjà
    cursor.execute("SELECT id FROM expansions WHERE code = %s", (set_code,))
    existing_expansion = cursor.fetchone()

    if existing_expansion:
        return existing_expansion['id']

    # Créer la nouvelle expansion
    print(f"🆕 Création de la nouvelle expansion: {set_code} - {set_name_en}")

    cursor.execute("""
        INSERT INTO expansions (code, name_en, region)
        VALUES (%s, %s, 'Thailand')
    """, (set_code, set_name_en))

    connection.commit()
    return cursor.lastrowid

def get_language_id(connection, language_code):
    """Récupère l'ID de la langue"""
    cursor = connection.cursor(dictionary=True)
    cursor.execute("SELECT id FROM languages WHERE code = %s", (language_code,))
    result = cursor.fetchone()
    return result['id'] if result else None

def card_exists_in_db(connection, web_id, language_id):
    """Vérifie si une carte existe déjà en base pour cette langue"""
    cursor = connection.cursor()
    cursor.execute("""
        SELECT cv.id FROM card_versions cv
        WHERE cv.web_id = %s AND cv.language_id = %s
    """, (str(web_id), language_id))
    result = cursor.fetchone()
    cursor.close()
    return result is not None

def get_or_create_card(connection, expansion_id, card_number, supertype='Pokémon'):
    """Récupère ou crée une carte maître"""
    cursor = connection.cursor(dictionary=True)

    # Chercher une carte existante avec ce numéro dans cette expansion
    cursor.execute("""
        SELECT id FROM cards
        WHERE canonical_number = %s AND expansion_id = %s
    """, (card_number, expansion_id))

    existing_card = cursor.fetchone()
    if existing_card:
        return existing_card['id']

    # Créer une nouvelle carte maître
    cursor.execute("""
        INSERT INTO cards (canonical_number, expansion_id, supertype)
        VALUES (%s, %s, %s)
    """, (card_number, expansion_id, supertype))

    connection.commit()
    return cursor.lastrowid

# Ancienne fonction supprimée - remplacée par sauvegarde directe

def main():
    """Fonction principale - Scraping complet thaï"""
    
    parser = argparse.ArgumentParser(description='Scraper Thaï Pokemon TCG')
    parser.add_argument('--test', action='store_true', help='Mode test (limite à 100 IDs)')
    args = parser.parse_args()
    
    print("🇹🇭 SCRAPER THAÏ POKEMON TCG")
    print("=" * 60)
    print("🎯 Objectif: Récupérer TOUTES les cartes depuis le site thaï")
    print("📊 Source: asia.pokemon-card.com/th/")
    print("=" * 60)
    
    # Connexion base de données
    try:
        connection = mysql.connector.connect(**get_db_config())
        print("✅ Connexion à la base de données établie")
    except Exception as e:
        print(f"❌ Erreur connexion base: {e}")
        return
    
    # Scraper toutes les cartes avec sauvegarde progressive
    if args.test:
        print("🧪 Mode test activé - limitation à 100 IDs")
        # Pour le test, on peut limiter la portée

    # NOUVELLE MÉTHODE: Sauvegarde progressive
    saved_count = scrape_all_thai_cards_progressive(connection)

    if saved_count == 0:
        print("❌ Aucune nouvelle carte sauvegardée")
    else:
        print(f"✅ {saved_count} nouvelles cartes sauvegardées avec succès")

    connection.close()

    print(f"\n" + "=" * 60)
    print("🎉 SCRAPING THAÏ TERMINÉ")
    print("=" * 60)
    print(f"✅ {saved_count} nouvelles cartes sauvegardées")
    print("✅ Sauvegarde progressive - Aucune perte de données possible")
    print("✅ Toutes les données thaï sauvegardées avec VRAIS sets")

if __name__ == "__main__":
    main()
