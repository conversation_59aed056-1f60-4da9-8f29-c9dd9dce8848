#!/usr/bin/env python3
"""
Lanceur simple pour Pokemon TCG Scraper
3 fonctionnalités essentielles uniquement
"""

import sys
import os

def main():
    """Menu principal"""
    
    print("🃏 POKEMON TCG SCRAPER - VERSION SIMPLIFIÉE")
    print("=" * 50)
    print("1. 🌐 Interface Web")
    print("2. 🇯🇵 Scraper Bulbapedia")
    print("3. 🇹🇭 Scraper Thai")
    print("4. 🇮🇩 Scraper Indonésien")
    print("0. ❌ Quitter")
    
    choice = input("\nChoisissez une option: ")
    
    if choice == "1":
        print("🌐 Lancement de l'interface web...")
        os.system("python3 scraping/app.py")
    elif choice == "2":
        print("🇯🇵 Lancement du scraper Bulbapedia...")
        os.system("python3 scraping/parse_bulbapedia.py")
    elif choice == "3":
        print("🇹🇭 Lancement du scraper Thai...")
        os.system("python3 scraping/scraping_thai.py")
    elif choice == "4":
        print("🇮🇩 Lancement du scraper Indonésien...")
        os.system("python3 scraping/scraping_indonesian.py")
    elif choice == "0":
        print("👋 Au revoir!")
        sys.exit(0)
    else:
        print("❌ Option invalide")
        main()

if __name__ == "__main__":
    main()
