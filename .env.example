# Pokemon TCG Scraper Database Configuration
# Copy this file to .env and update with your actual database credentials

# Database connection settings
DB_HOST=localhost
DB_PORT=10004
DB_USER=root
DB_PASSWORD=root
DB_DATABASE=pokemon_tcg_db

# Unix socket (leave empty if not using socket connection)
# Common MAMP socket path: /Applications/MAMP/tmp/mysql/mysql.sock
# Common XAMPP socket path: /tmp/mysql.sock
DB_UNIX_SOCKET=/Applications/MAMP/tmp/mysql/mysql.sock

# Optional: Override default charset
# DB_CHARSET=utf8mb4
