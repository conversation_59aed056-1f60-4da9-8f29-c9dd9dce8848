# Pokemon TCG Scraper - Version Simplifiée

## 🎯 Fonctionnalités

Ce projet contient **3 fonctionnalités essentielles** :

1. **🇯🇵 Scraper Bulbapedia** - `scraping/parse_bulbapedia.py`
2. **🇹🇭 Scraper Thai** - `scraping/scraping_thai.py`  
3. **🇮🇩 Scraper Indonésien** - `scraping/scraping_indonesian.py`

## 🚀 Utilisation

### Méthode 1: Lance<PERSON> simple
```bash
python3 launcher.py
```

### Méthode 2: Interface web
```bash
python3 scraping/app.py
```

### Méthode 3: Scrapers individuels
```bash
# Scraper Bulbapedia
python3 scraping/parse_bulbapedia.py

# Scraper Thai
python3 scraping/scraping_thai.py

# Scraper Indonésien
python3 scraping/scraping_indonesian.py
```

## 📁 Structure

```
pokemon-tcg-scraper/
├── launcher.py              # Lanceur simple
├── scraping/
│   ├── app.py              # Interface web
│   ├── parse_bulbapedia.py # Scraper Bulbapedia
│   ├── scraping_thai.py    # Scraper Thai
│   └── scraping_indonesian.py # Scraper Indonésien
├── config/                 # Configuration
├── database/              # Base de données
└── images/               # Images des cartes
```

## ⚙️ Configuration

La configuration se trouve dans `config/database.py`.

## 🗄️ Base de données

Le schéma est défini dans `db_structure.sql`.
